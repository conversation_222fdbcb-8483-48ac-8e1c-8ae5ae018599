import 'package:data/data.dart';
import 'package:domain/usecase/order/order_use_case.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:printing/printing.dart';
import 'package:services/services.dart';
import 'package:socket_manager/socket_manager.dart';

import '../../common.dart';

class PrintKitchenItemsPageArgs {
  final Order order;
  final void Function()? onSuccess;
  const PrintKitchenItemsPageArgs({required this.order, this.onSuccess});
}

class PrintKitchenItemsPage extends StatefulWidget {
  static const routeName = '/sales/ordering/print_kitchen_items';

  final PrintKitchenItemsPageArgs args;

  const PrintKitchenItemsPage({required this.args, super.key});

  @override
  State<PrintKitchenItemsPage> createState() => _PrintKitchenItemsPageState();
}

class _PrintKitchenItemsPageState extends State<PrintKitchenItemsPage> {
  Order get _order => widget.args.order;
  List<OrderItem> get _items => _order.orderItems;

  final List<bool> _selected = [];

  @override
  void initState() {
    super.initState();
    _selected.addAll(
      List.generate(
        _items.length,
        (index) => _items[index].timesPrinted == null || _items[index].timesPrinted! == 0,
      ),
    );
  }

  void _toggleItemSelection(int itemIndex) {
    setState(() {
      _selected[itemIndex] = !_selected[itemIndex];
    });
  }

  void _selectAll() {
    setState(() {
      for (int i = 0; i < _selected.length; i++) {
        if (_isShow(_items[i])) {
          _selected[i] = true;
        }
      }
    });
  }

  void _deselectAll() {
    setState(() {
      for (int i = 0; i < _selected.length; i++) {
        _selected[i] = false;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final List<({int index, OrderItem item})> visibleItems = [];
    for (int i = 0; i < _items.length; i++) {
      if (_isShow(_items[i])) {
        visibleItems.add((index: i, item: _items[i]));
      }
    }

    final hasItems = visibleItems.isNotEmpty;

    final allSelected = hasItems && visibleItems.every((item) => _selected[item.index]);
    final anySelected = hasItems && visibleItems.any((item) => _selected[item.index]);

    return CScaffold(
      appBar: const CAppBar(title: 'In bếp'),
      body: Column(
        children: hasItems
            ? <Widget>[
                Material(
                  color: CColors.white,
                  child: CCheckboxListTile(
                    padding: CEdgeInsets.a16,
                    value: allSelected,
                    onChanged: (_) {
                      if (allSelected) {
                        _deselectAll();
                      } else {
                        _selectAll();
                      }
                    },
                    titlePadding: CEdgeInsets.a0,
                    titleWidget: CText.body(
                      allSelected ? 'Bỏ chọn tất cả'.untranslated : 'Chọn tất cả'.untranslated,
                    ),
                  ),
                ),
                const CDivider.indented(),
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        for (int i = 0; i < visibleItems.length; i++) ...[
                          if (i > 0) const CDivider.indented(),
                          _buildItem(visibleItems[i].index, visibleItems[i].item),
                        ],
                      ],
                    ),
                  ),
                ),
                _buildFooter(anySelected: anySelected),
              ]
            : [
                Expanded(
                  child: Center(
                    child: CText.title(
                      'Đơn hàng trống'.untranslated,
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
              ],
      ),
    );
  }

  bool _isShow(OrderItem item) {
    if (item.isChild) return false;
    if (item.status == OrderStatusEnum.merged.id) return false;
    if (item.status == OrderStatusEnum.cancelled.id) return false;
    if (item.printPlaces.isNullOrEmpty) return false;
    return true;
  }

  Widget _buildFooter({bool anySelected = false}) {
    return ColoredBox(
      color: CColors.white,
      child: Padding(
        padding: CEdgeInsets.a12,
        child: CButton.custom(
          label: 'In bếp'.untranslated,
          backgroundColor: AppColors.current.primaryColor,
          onTap: _handlePrintKitchen,
          enabled: anySelected,
        ),
      ),
    );
  }

  Widget _buildItem(int index, OrderItem item) {
    final isSelected = _selected[index];
    return Material(
      color: CColors.white,
      child: Padding(
        padding: CEdgeInsets.a16.byV6(),
        child: CCheckboxListTile(
          value: isSelected,
          onChanged: (_) {
            _toggleItemSelection(index);
          },
          titlePadding: CEdgeInsets.a0,
          titleWidget: Row(
            children: [
              CText.body(
                '${NumberFormatUtils.format(item.number, isCurrency: false)}x ',
                fontWeight: FontWeight.bold,
                color: AppColors.current.secondaryColor,
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CText.body(
                      item.translatedName,
                      maxLines: null,
                      overflow: TextOverflow.ellipsis,
                    ),
                    if (item.timesPrinted != null && item.timesPrinted! > 0)
                      CText.caption(
                        '(đã in)',
                        color: AppColors.current.secondaryColor,
                      ),
                    for (final option in item.options)
                      CText.caption(
                        () {
                          String name = option.name;
                          if (option.unitName.isNotNullOrEmpty) name += ' (${option.unitName})';
                          return '+ $name';
                        }(),
                      ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _handlePrintKitchen() async {
    final selectedItems = <OrderItem>[];

    for (int i = 0; i < _items.length; i++) {
      if (_selected[i]) {
        selectedItems.add(_items[i]);
      }
    }

    if (selectedItems.isEmpty) {
      ViewUtils.showToastError('Vui lòng chọn món để in bếp');
      return;
    }

    ViewUtils.showLoading();

    try {
      if (CommonFunc.isPda()) {
        // Send print request to cashier from PDA
        final selectedItemIds = selectedItems.map((e) => e.orderProductLocalId).toList();

        final data = DataFromPDA.rePrint(
          _order.localId,
          typePrint: [TypePrint.kitchen],
          printKitchenItemIds: selectedItemIds,
          order: _order,
        );

        final messageHandler = GetIt.I.get<MessageHandler>();
        final response = await messageHandler.sendMessageToCashier(data);

        await response.processResult(
          onSuccess: (res) {
            if (res.order != null) {
              GetIt.I<OrderUseCase>().saveOrder(res.order!);
            }

            if (mounted) Navigator.of(context).pop();
            widget.args.onSuccess?.call();
            ViewUtils.showToastSuccess('In bếp thành công'.untranslated);
          },
          onTimeout: (res) {
            ViewUtils.showToastError(res.errorMessage ?? 'Không nhận được phản hồi từ máy chủ'.untranslated);
          },
          onFailure: (res, _) {
            ViewUtils.showToastError(res.errorMessage ?? 'In bếp thất bại'.untranslated);
          },
        );
      } else {
        // Cashier logic
        // Create a clone of the order with only the selected items
        final orderForPrinting = _order.clone();
        orderForPrinting.orderItems.assignAll(selectedItems);

        final result = await Printing.i.print(
          orderForPrinting,
          typePrint: TypePrint.kitchen,
          
        );

        if (result.itemPrinted.isNotEmpty) {
          final order = GetIt.I<OrderUseCase>().loadOrder(orderForPrinting.localId)!;
          order.syncFlag = SyncFlag.prepareSync;
          order.versionCode += 1;
          order.updateTime(all: false);
          for (final item in order.orderItems) {
            if (result.itemPrinted.contains(item.orderProductLocalId)) {
              item.timesPrinted = (item.timesPrinted ?? 0) + 1;
              item.orderProductVersionCode += 1;
              item.lastUpdateLocal = DateTime.now().toStringWithFormat(DateTimeFormatConstants.uiDateYmdTimehms);
            }
          }

          SaveOrderHelper.saveOrder(
            order,
            source: OrderSource.ui,
            priority: OrderProcessingPriority.ui,
          ).ignore();
        }

        if (mounted) Navigator.of(context).pop();
        widget.args.onSuccess?.call();
        if (result.isSuccess) ViewUtils.showToastSuccess('In bếp thành công'.untranslated);
      }
    } catch (e, s) {
      LogUtils.error('Error in _handlePrintKitchen: $e', s, pushSentry: true);
      ViewUtils.showToastError('Đã có lỗi xảy ra: $e'.untranslated);
    } finally {
      ViewUtils.closeLoading();
    }
  }
}
