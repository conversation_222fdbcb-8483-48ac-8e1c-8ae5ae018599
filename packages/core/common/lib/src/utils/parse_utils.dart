import 'dart:convert';

class ParseUtils {
  const ParseUtils._();

  static String? getString(dynamic value) {
    if (value == null || value == 'null') return null;

    return value.toString();
  }

  static int? getInt(dynamic value) {
    if (value is double) {
      return value.toInt();
    }
    if (value is bool) {
      return value ? 1 : 0;
    }
    return int.tryParse(value.toString()) ?? double.tryParse(value.toString())?.toInt();
  }

  static double? getDouble(dynamic value) {
    return double.tryParse(value.toString());
  }

  static bool? getBool(dynamic value) {
    if ([true, 'true', 1, '1'].contains(value)) {
      return true;
    }
    if ([false, 'false', 0, '0'].contains(value)) {
      return false;
    }
    return null;
  }

  static double? sanitizeDouble(double? value) {
    if (value == null) return null;
    if (value.isNaN) {
      return 0.0;
    }
    return value;
  }

  static List<T>? getPrimitiveList<T>(dynamic input) {
    try {
      final value = input is String ? jsonDecode(input) : input;

      if (value is List) {
        final List<T> result = [];
        for (final item in value) {
          if (item is T) {
            result.add(item);
          } else {
            final str = item.toString();
            if (T == int) {
              result.add((getInt(str) ?? 0) as T);
            } else if (T == double) {
              result.add((getDouble(str) ?? 0) as T);
            } else if (T == String) {
              result.add(str as T);
            } else if (T == Map<String, dynamic>) {
              if (item is String) {
                result.add(jsonDecode(item) as T);
              }
            }
          }
        }

        return result;
      }
    } catch (_) {}

    return null;
  }

  static Map<K, V>? getPrimitiveMap<K, V>(dynamic input) {
    Map<dynamic, dynamic>? rawMap;
    if (input is String) {
      final dec = jsonDecode(input);
      if (dec is! Map) return null;
      rawMap = dec;
    } else if (input is Map) {
      rawMap = input;
    }
    if (rawMap == null) return null;

    final Map<K, V> result = {};

    for (final key in rawMap.keys) {
      final K? expectedKey = getPrimitive(key);
      final V? expectedValue = getPrimitive(rawMap[key]);
      if (expectedKey == null || expectedValue == null) return null;
      result[expectedKey] = expectedValue;
    }

    return result;
  }

  static T? getPrimitive<T>(dynamic input) {
    if (input is T) {
      return input;
    }

    if (T == int) {
      return getInt(input) as T?;
    } else if (T == double) {
      return getDouble(input) as T?;
    } else if (T == String) {
      return getString(input) as T?;
    } else if (T == bool) {
      return getBool(input) as T?;
    }

    return null;
  }

  static List<T>? getList<T>(dynamic input, T Function(Map<String, dynamic>) fromJson) {
    try {
      final value = input is String ? json.decode(input) : input;

      if (value is List) {
        final List<T> result = [];
        for (final item in value) {
          final map = item is String ? json.decode(item) : item;
          if (map is Map<dynamic, dynamic>) {
            final mapString = map.map((k, v) => MapEntry(k.toString(), v));
            result.add(fromJson(mapString));
          } else if (map is Map<String, dynamic>) {
            result.add(fromJson(map));
          }
        }

        return result;
      }
    } catch (_) {}

    return null;
  }

  static Map<String, dynamic>? getMap(dynamic input) {
    try {
      final value = input is String ? jsonDecode(input) : input;

      if (value is Map<dynamic, dynamic>) {
        return value.map((k, v) => MapEntry(k.toString(), v));
      }

      if (value is Map<String, dynamic>) {
        return value;
      }
    } catch (_) {}

    return null;
  }

  static List<Map<String, dynamic>>? getListMap(dynamic input) {
    try {
      final value = input is String ? jsonDecode(input) : input;

      if (value is List) {
        final List<Map<String, dynamic>> result = [];
        for (final item in value) {
          if (item is Map<dynamic, dynamic>) {
            final map = item.map((k, v) => MapEntry(k.toString(), v));
            result.add(map);
          } else if (item is Map<String, dynamic>) {
            result.add(item);
          }
        }

        return result;
      }

      if (value is Map<String, dynamic>) {
        return [value];
      }
    } catch (_) {}

    return null;
  }

  static T? fromJsonNullable<T>(dynamic json, T Function(Map<String, dynamic> json) fromJson) {
    try {
      final value = json is String ? jsonDecode(json) : json;
      if (value is Map<String, dynamic>) {
        return fromJson(value);
      } else if (value is Map<dynamic, dynamic>) {
        final map = value.map((k, v) => MapEntry(k.toString(), v));
        return fromJson(map);
      }
    } catch (_) {}
    return null;
  }

  static T? transformIfNotNull<T, R>(R? input, T Function(R nonNullableInput) transform) {
    return input == null ? null : transform(input);
  }
}
