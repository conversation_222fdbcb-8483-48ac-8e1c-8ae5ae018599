import 'package:collection/collection.dart';
import 'package:data/data.dart';
import 'package:domain/base/base_param.dart';
import 'package:domain/dto/payment_method_dto.dart';
import 'package:domain/usecase/order/order_params.dart';
import 'package:domain/usecase/order/order_use_case.dart';
import 'package:domain/usecase/setting/setting_use_case.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:printing/printing.dart';
import 'package:resources/resources.dart';
import '../../../../common.dart';

part 'component/order_status.dart';
part 'component/customer_info.dart';
part 'component/order_detail.dart';
part 'component/price_tile.dart';
part 'component/item_options.dart';
part 'component/item_combo_promotion.dart';
part 'component/order_price_detail.dart';
part 'component/payment_detail.dart';

class CDetailOrderPage extends StatelessWidget {
  final Order order;
  final VoidCallback? onCanceled;

  const CDetailOrderPage({
    required this.order,
    this.onCanceled,
    super.key,
  });

  static Future<void> show({
    required BuildContext context,
    required Order order,
    VoidCallback? onCanceled,
  }) {
    return Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CDetailOrderPage(
          order: order,
          onCanceled: onCanceled,
        ),
      ),
    );
  }

  Future<int> _getTaxType() async {
    final isAdmin = GetIt.I<AppInfo>().isAdmin;
    if (!isAdmin) {
      final shopOptions = GetIt.I<SettingUseCase>().getShopOptionsLocal();
      return shopOptions.typeTaxCalculation;
    }
    final shopOptions = await GetIt.I<SettingUseCase>().getShopOptions(
      BaseParam(shopId: order.shopId),
    );
    return shopOptions.typeTaxCalculation;
  }

  @override
  Widget build(BuildContext context) {
    final isTablet = AppDimen.current.isTablet;
    return CScaffold(
      appBar: CAppBar(
        title: S.current.orderDetail,
        actions: [
          CancelOrderActionButton(
            order: order,
            onSuccess: () => onCanceled?.call(),
          ),
        ],
      ),
      body: FutureBuilder(
        future: _getTaxType(),
        builder: (context, snapshot) {
          return CLoading.skeleton(
            isLoading: snapshot.connectionState == ConnectionState.waiting,
            child: isTablet
                ? _layoutTablet(taxType: snapshot.data ?? 0, context: context)
                : _layoutMobile(taxType: snapshot.data ?? 0, context: context),
          );
        },
      ),
    );
  }

  Widget _layoutMobile({required int taxType, required BuildContext context}) {
    return Container(
      color: CColors.white,
      height: double.infinity,
      child: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: CEdgeInsets.a16,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _OrderStatus(order: order),
                  if (order.customerName.isNotNullOrEmpty)
                    _CustomerInfo(
                      customerName: order.customerName!,
                      customerPhone: order.customerPhoneNumber,
                    ),
                  _OrderDetail(order: order, taxType: taxType),
                ].addBetweenEvery(const CDivider(height: 24)),
              ),
            ),
          ),
          _buildPrintOptions(context: context),
        ],
      ),
    );
  }

  Widget _layoutTablet({required int taxType, required BuildContext context}) {
    return Row(
      children: [
        Expanded(
          flex: 2,
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Container(
              height: double.infinity,
              padding: CEdgeInsets.a16,
              decoration: const BoxDecoration(
                color: CColors.white,
                borderRadius: CBorderRadius.c8,
              ),
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    _OrderDetail(order: order, taxType: taxType),
                  ],
                ),
              ),
            ),
          ),
        ),
        Expanded(
          child: Container(
            color: CColors.white,
            padding: CEdgeInsets.a16,
            child: Column(
              children: [
                if (order.customerName.isNotNullOrEmpty)
                  _CustomerInfo(
                    customerName: order.customerName!,
                    customerPhone: order.customerPhoneNumber,
                  ),
                Expanded(
                  child: Center(
                    child: _OrderStatus(order: order),
                  ),
                ),
                _buildPrintOptions(context: context),
              ].addBetweenEvery(const CDivider(height: 24)),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPrintOptions({required BuildContext context}) {
    return Column(
      children: [
        if (order.status != OrderStatusEnum.cancelled.id &&
            !(order.status == OrderStatusEnum.pending.id && order.channelId == 2))
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              if (Permission.reprintKitchenOrder.isGranted)
                _buildButton(
                  label: 'In bếp'.untranslated,
                  icon: MyAssets.svgPrintForKitchen,
                  onTap: () {
                    final args = PrintKitchenItemsPageArgs(
                      order: order,
                    );
                    ViewUtils.openPage(
                      context,
                      routeName: PrintKitchenItemsPage.routeName,
                      arguments: args,
                      pageBuilder: () => PrintKitchenItemsPage(args: args),
                    );
                  },
                ),
              if (CommonFunc.isCashier() && order.isPaid && !order.isTopup && Permission.reprintPaymentOrder.isGranted)
                _buildButton(
                  label: 'In hóa đơn'.untranslated,
                  icon: MyAssets.svgPrint2,
                  onTap: () async {
                    if (order.isExchangeGift()) {
                      await Printing.i.print(order, typePrint: TypePrint.exchangeGift);
                    } else {
                      await Printing.i.print(order);
                    }
                  },
                ),
            ].addBetweenEvery(const SizedBox(width: 12)),
          ),
      ],
    );
  }

  Widget _buildButton({
    required String label,
    required SvgGenImage icon,
    required VoidCallback onTap,
  }) {
    return Material(
      color: CColors.white,
      child: InkWell(
        splashColor: CColors.burningOrange.withOpacity(0.2),
        splashFactory: InkSplash.splashFactory,
        borderRadius: CBorderRadius.c3,
        onTap: onTap,
        child: Padding(
          padding: CEdgeInsets.a4,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CIcon.svg(icon, width: Dimens.d24, height: Dimens.d36, color: CColors.burningOrange),
              CText.callout(label),
            ],
          ),
        ),
      ),
    );
  }
}
