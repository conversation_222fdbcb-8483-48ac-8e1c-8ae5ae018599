// ignore_for_file: unnecessary_this

import 'dart:convert';

import 'package:common/common.dart';
import 'package:get_it/get_it.dart';
import 'package:network/network.dart';
import 'package:objectbox/objectbox.dart';

import 'product.dart';
import 'product_option_item.dart';

@Entity()
class OrderItem {
  @Id()
  int? id;
  @Unique(onConflict: ConflictStrategy.replace)
  final String orderProductLocalId;
  final String orderId;
  final int shopId;
  final int productId;
  final int productMasterId;
  int orderProductVersionCode;
  String productName;
  int? productExtendId;
  String? batchNo;
  String? batchExpireDate;
  int? unitId;
  String? unitName;
  double? primaryUnitConvert;
  double number;
  double price;
  double discountPrice;
  double discountPercent;
  String? discountReason;
  int? discountId;
  double productTaxPercent;
  double productTaxPrice;
  String? cancelReason;
  String? transferFrom;
  String? noteProduct;
  int? shiftId;
  String? paidDate;
  int? type;
  String? extraName;
  String? priceExtra;
  String? regdate;
  String? lastUpdate;
  int? status;
  int? orderProductStatus;
  int? orderProductExtraStatus;
  String? regdateLocal;
  String? lastUpdateLocal;
  String? printDateLocal;
  String? nameSortOrderFood;
  int? orderIndexOrderFood;
  String? extra;
  String? paidRegdate;
  int? orderProductInvalid;
  double? orderProductInprice;
  String? orderProductBusinessDate;
  int? promotionDetailId;
  String? originalReflocalitem;
  String? itemextraMemo1;
  String? itemextraMemo2;
  int? pricePolicyId;
  int? orderProductAccountId;
  String? orderProductAccountName;
  int? orderProductCategoryId;
  String? orderProductCategoryName;
  String? orderProductParentId;
  double? inventoryCreatedFlg;
  String? commission;
  int? productType;
  int? tableId;
  String? tableName;
  int? areaId;
  String? areaName;
  String? fromTime;
  String? toTime;
  int? unitType;
  int? productUnitType;
  String? itemAccountServiceIds;
  int? batchId;
  double? originalPrice;
  double grandTotal;
  String? startTime;
  String? endTime;
  double productPrice;
  int? cardType;
  String? expiration;
  int? verifyOtpFlg;
  int? priceEditableFlg;
  double? cardAmount;
  double? cardDiscountPercent;
  int? cardDecreaseCommissionFlg;
  int? createdCustomerInventoryFlg;
  int? customerTreatmentCardUseFlg;
  int? customerTreatmentCardId;
  int? customerTreatmentCardUseQuantity;
  String? printPlaces;
  double surchargePrice;
  double surchargePercent;
  String? surchargeReason;
  int? surchargeId;
  int? timesPrinted;
  String? productNameTranslations;
  int? orderProductSourceType;
  String? orderProductSourceId;
  int? orderProductToppingFlg;
  double comboPrice;
  String? partnerWarehouseCode;
  String? timeOfUse;
  String? productCode;

  final options = ToMany<ProductOptionItem>();

  @Transient()
  bool isSelected = false;

  @Transient()
  List<OrderItem> comboChild = [];

  @Transient()
  int? unitTime;

  @Transient()
  int? unitValue;

  /// dùng cho hàm chia đơn đối với đa kho
  @Transient()
  String? originalLocalId;

  // lưu số lượng trước khi trả món
  @Transient()
  double? originalNumber;

  OrderItem({
    required this.orderProductLocalId,
    required this.orderId,
    required this.shopId,
    required this.productId,
    required this.productMasterId,
    required this.productName,
    this.orderProductVersionCode = 1,
    this.productExtendId,
    this.batchNo,
    this.batchExpireDate,
    this.unitId,
    this.unitName,
    this.primaryUnitConvert,
    this.number = 0.0,
    this.price = 0.0,
    this.discountPrice = 0.0,
    this.discountPercent = 0.0,
    this.discountReason = '',
    this.discountId = 0,
    this.productTaxPercent = 0.0,
    this.productTaxPrice = 0.0,
    this.cancelReason,
    this.transferFrom,
    this.noteProduct,
    this.shiftId,
    this.paidDate,
    this.type,
    this.extraName,
    this.priceExtra,
    this.regdate,
    this.lastUpdate,
    this.status,
    this.orderProductStatus,
    this.orderProductExtraStatus,
    this.regdateLocal,
    this.lastUpdateLocal,
    this.printDateLocal,
    this.nameSortOrderFood,
    this.orderIndexOrderFood,
    this.extra,
    this.paidRegdate,
    this.orderProductInvalid,
    this.orderProductInprice,
    this.orderProductBusinessDate,
    this.promotionDetailId,
    this.originalReflocalitem,
    this.itemextraMemo1,
    this.itemextraMemo2,
    this.pricePolicyId,
    this.orderProductAccountId,
    this.orderProductAccountName,
    this.orderProductCategoryId,
    this.orderProductCategoryName,
    this.orderProductParentId,
    this.inventoryCreatedFlg = -1,
    this.commission,
    this.productType,
    this.tableId,
    this.tableName,
    this.areaId,
    this.areaName,
    this.fromTime,
    this.toTime,
    this.unitType,
    this.productUnitType,
    this.itemAccountServiceIds,
    this.batchId,
    this.originalPrice,
    this.grandTotal = 0.0,
    this.startTime,
    this.endTime,
    this.productPrice = 0.0,
    this.cardType,
    this.expiration,
    this.verifyOtpFlg,
    this.priceEditableFlg,
    this.cardAmount,
    this.cardDiscountPercent,
    this.cardDecreaseCommissionFlg,
    this.createdCustomerInventoryFlg,
    this.customerTreatmentCardUseFlg,
    this.customerTreatmentCardId,
    this.customerTreatmentCardUseQuantity,
    this.printPlaces = '',
    this.surchargePercent = 0.0,
    this.surchargePrice = 0.0,
    this.surchargeReason = '',
    this.surchargeId = 0,
    this.timesPrinted = 0,
    this.productNameTranslations,
    this.orderProductSourceType,
    this.orderProductSourceId,
    this.orderProductToppingFlg,
    this.isSelected = false,
    this.comboChild = const [],
    this.comboPrice = 0,
    this.unitTime,
    this.unitValue,
    this.partnerWarehouseCode,
    this.originalLocalId,
    this.timeOfUse,
    this.originalNumber,
    this.productCode,
  });

  factory OrderItem.fromResponse(OrderItemResponse response) {
    final data = OrderItem(
      orderProductLocalId: response.id!,
      orderId: response.orderId!,
      shopId: response.shopId!,
      orderProductVersionCode: response.orderProductVersionCode ?? 1,
      productId: response.productId!,
      productMasterId: response.productMasterId!,
      productName: response.productName ?? '',
      productExtendId: response.productExtendId,
      batchNo: response.batchNo,
      batchExpireDate: response.batchExpireDate,
      unitId: response.unitId,
      unitName: response.unitName,
      primaryUnitConvert: response.primaryUnitConvert,
      number: response.number ?? 1,
      price: response.price ?? 0,
      discountPrice: response.discountPrice ?? 0,
      discountPercent: response.discountPercent ?? 0,
      discountReason: response.discountReason,
      discountId: response.discountId,
      productTaxPercent: response.productTaxPercent ?? 0,
      productTaxPrice: response.productTaxPrice ?? 0,
      cancelReason: response.cancelReason,
      transferFrom: response.transferFrom,
      noteProduct: response.noteProduct,
      shiftId: response.shiftId,
      paidDate: response.paidDate,
      type: response.type,
      extraName: response.extraName,
      priceExtra: response.priceExtra,
      regdate: response.regdate,
      lastUpdate: response.lastUpdate,
      status: response.status,
      orderProductStatus: response.orderProductStatus,
      orderProductExtraStatus: response.orderProductExtraStatus,
      regdateLocal: response.regdateLocal,
      lastUpdateLocal: response.lastUpdateLocal,
      printDateLocal: response.printDateLocal,
      nameSortOrderFood: response.nameSortOrderFood,
      orderIndexOrderFood: response.orderIndexOrderFood,
      extra: response.extra,
      orderProductBusinessDate: response.orderProductBusinessDate,
      promotionDetailId: response.promotionDetailId,
      originalReflocalitem: response.originalReflocalitem,
      itemextraMemo1: response.itemextraMemo1,
      itemextraMemo2: response.itemextraMemo2,
      pricePolicyId: response.pricePolicyId,
      orderProductAccountId: response.orderProductAccountId,
      orderProductAccountName: response.orderProductAccountName,
      orderProductCategoryId: response.orderProductCategoryId,
      orderProductCategoryName: response.orderProductCategoryName,
      orderProductParentId: response.orderProductParentId,
      inventoryCreatedFlg: response.inventoryCreatedFlg,
      commission: response.commission,
      productType: response.productType,
      tableId: response.tableId,
      tableName: response.tableName,
      areaId: response.areaId,
      areaName: response.areaName,
      fromTime: response.fromTime,
      toTime: response.toTime,
      unitType: response.unitType,
      productUnitType: response.productUnitType,
      itemAccountServiceIds: response.itemAccountServiceIds,
      batchId: response.batchId,
      originalPrice: response.originalPrice,
      grandTotal: response.grandTotal ?? 0,
      startTime: response.startTime,
      endTime: response.endTime,
      productPrice: response.productPrice ?? 0,
      cardType: response.cardType,
      expiration: response.expiration,
      verifyOtpFlg: response.verifyOtpFlg,
      priceEditableFlg: response.priceEditableFlg,
      cardAmount: response.cardAmount,
      cardDiscountPercent: response.cardDiscountPercent,
      cardDecreaseCommissionFlg: response.cardDecreaseCommissionFlg,
      createdCustomerInventoryFlg: response.createdCustomerInventoryFlg,
      surchargeReason: response.surchargeReason,
      surchargeId: response.surchargeId,
      timesPrinted: response.timesPrinted,
      productNameTranslations: response.productNameTranslations,
      orderProductSourceType: response.orderProductSourceType,
      orderProductSourceId: response.orderProductSourceId,
      orderProductToppingFlg: response.orderProductToppingFlg,
      comboPrice: response.comboPrice ?? 0,
      printPlaces: response.printPlaces ?? '',
      partnerWarehouseCode: response.partnerWarehouseCode,
      timeOfUse: response.timeOfUse,
    );

    final options = ParseUtils.getList(
          response.options,
          (e) => ProductOptionItem.fromMap(e, data.productId, data.orderProductLocalId),
        ) ??
        [];
    data.options.assignAll(options);
    return data;
  }

  OrderItem clone({
    String? orderProductLocalId,
    String? orderId,
  }) {
    final item = OrderItem(
      orderProductLocalId: orderProductLocalId ?? this.orderProductLocalId,
      orderId: orderId ?? this.orderId,
      shopId: shopId,
      orderProductVersionCode: orderProductVersionCode,
      productId: productId,
      productMasterId: productMasterId,
      productName: productName,
      productExtendId: productExtendId,
      batchNo: batchNo,
      batchExpireDate: batchExpireDate,
      unitId: unitId,
      unitName: unitName,
      primaryUnitConvert: primaryUnitConvert,
      number: number,
      price: price,
      discountPrice: discountPrice,
      discountPercent: discountPercent,
      discountReason: discountReason,
      discountId: discountId,
      productTaxPercent: productTaxPercent,
      productTaxPrice: productTaxPrice,
      cancelReason: cancelReason,
      transferFrom: transferFrom,
      noteProduct: noteProduct,
      shiftId: shiftId,
      paidDate: paidDate,
      type: type,
      extraName: extraName,
      priceExtra: priceExtra,
      regdate: regdate,
      lastUpdate: lastUpdate,
      status: status,
      orderProductStatus: orderProductStatus,
      orderProductExtraStatus: orderProductExtraStatus,
      regdateLocal: regdateLocal,
      lastUpdateLocal: lastUpdateLocal,
      printDateLocal: printDateLocal,
      nameSortOrderFood: nameSortOrderFood,
      orderIndexOrderFood: orderIndexOrderFood,
      extra: extra,
      paidRegdate: paidRegdate,
      orderProductInvalid: orderProductInvalid,
      orderProductInprice: orderProductInprice,
      orderProductBusinessDate: orderProductBusinessDate,
      promotionDetailId: promotionDetailId,
      originalReflocalitem: originalReflocalitem,
      itemextraMemo1: itemextraMemo1,
      itemextraMemo2: itemextraMemo2,
      pricePolicyId: pricePolicyId,
      orderProductAccountId: orderProductAccountId,
      orderProductAccountName: orderProductAccountName,
      orderProductCategoryId: orderProductCategoryId,
      orderProductCategoryName: orderProductCategoryName,
      orderProductParentId: orderProductParentId,
      inventoryCreatedFlg: inventoryCreatedFlg,
      commission: commission,
      productType: productType,
      tableId: tableId,
      tableName: tableName,
      areaId: areaId,
      areaName: areaName,
      fromTime: fromTime,
      toTime: toTime,
      unitType: unitType,
      productUnitType: productUnitType,
      itemAccountServiceIds: itemAccountServiceIds,
      batchId: batchId,
      originalPrice: originalPrice,
      grandTotal: grandTotal,
      startTime: startTime,
      endTime: endTime,
      productPrice: productPrice,
      cardType: cardType,
      expiration: expiration,
      verifyOtpFlg: verifyOtpFlg,
      priceEditableFlg: priceEditableFlg,
      cardAmount: cardAmount,
      cardDiscountPercent: cardDiscountPercent,
      cardDecreaseCommissionFlg: cardDecreaseCommissionFlg,
      createdCustomerInventoryFlg: createdCustomerInventoryFlg,
      customerTreatmentCardUseFlg: customerTreatmentCardUseFlg,
      customerTreatmentCardId: customerTreatmentCardId,
      customerTreatmentCardUseQuantity: customerTreatmentCardUseQuantity,
      printPlaces: printPlaces,
      surchargePrice: surchargePrice,
      surchargePercent: surchargePercent,
      surchargeReason: surchargeReason,
      surchargeId: surchargeId,
      timesPrinted: timesPrinted,
      productNameTranslations: productNameTranslations,
      orderProductSourceType: orderProductSourceType,
      orderProductSourceId: orderProductSourceId,
      orderProductToppingFlg: orderProductToppingFlg,
      comboPrice: comboPrice,
      unitTime: unitTime,
      unitValue: unitValue,
      partnerWarehouseCode: partnerWarehouseCode,
      originalLocalId: originalLocalId,
      timeOfUse: timeOfUse,
      originalNumber: originalNumber,
      productCode: productCode,
    );

    item.options.assignAll(options.map((e) => e.clone()));
    item.isSelected = isSelected;
    item.comboChild = comboChild.map((e) => e.clone()).toList();

    return item;
  }

  Map<String, dynamic> toMap({
    bool svMode = false,
  }) {
    final optionsData = options.map((option) => option.toMap()).toList();
    return {
      'order_product_local_id': orderProductLocalId,
      'order_id': orderId,
      'shop_id': shopId,
      'order_product_version_code': orderProductVersionCode,
      'product_id': productId,
      'product_master_id': productMasterId,
      'product_name': productName,
      'product_extend_id': productExtendId,
      'batch_no': batchNo,
      'batch_expire_date': batchExpireDate,
      'unit_id': unitId,
      'unit_name': unitName,
      'primary_unit_convert': ParseUtils.sanitizeDouble(primaryUnitConvert),
      'number': ParseUtils.sanitizeDouble(number),
      'price': ParseUtils.sanitizeDouble(price),
      'discount_price': ParseUtils.sanitizeDouble(discountPrice),
      'discount_percent': ParseUtils.sanitizeDouble(discountPercent),
      'discount_reason': discountReason,
      'discount_id': discountId,
      'product_tax_percent': ParseUtils.sanitizeDouble(productTaxPercent),
      'product_tax_price': ParseUtils.sanitizeDouble(productTaxPrice),
      'cancel_reason': cancelReason,
      'transfer_from': transferFrom,
      'note_product': noteProduct,
      'shift_id': shiftId,
      'paid_date': paidDate,
      'type': type,
      'extra_name': extraName,
      'price_extra': priceExtra,
      'regdate': regdate,
      'last_update': lastUpdate,
      'status': status,
      'order_product_status': orderProductStatus,
      'order_product_extra_status': orderProductExtraStatus,
      'regdate_local': regdateLocal,
      'last_update_local': lastUpdateLocal,
      'print_date_local': printDateLocal,
      'name_sort_order_food': nameSortOrderFood,
      'order_index_order_food': orderIndexOrderFood,
      'extra': extra,
      'paid_regdate': paidRegdate,
      'order_product_invalid': orderProductInvalid,
      'order_product_inprice': ParseUtils.sanitizeDouble(orderProductInprice),
      'order_product_business_date': orderProductBusinessDate,
      'promotion_detail_id': promotionDetailId,
      'original_reflocalitem': originalReflocalitem,
      'itemextra_memo1': itemextraMemo1,
      'itemextra_memo2': itemextraMemo2,
      'price_policy_id': pricePolicyId,
      'order_product_account_id': orderProductAccountId,
      'order_product_account_name': orderProductAccountName,
      'order_product_category_id': orderProductCategoryId,
      'order_product_category_name': orderProductCategoryName,
      'order_product_parent_id': orderProductParentId,
      'inventory_created_flg': ParseUtils.sanitizeDouble(inventoryCreatedFlg),
      'commission': commission,
      'product_type': productType,
      'table_id': tableId,
      'table_name': tableName,
      'area_id': areaId,
      'area_name': areaName,
      'from_time': fromTime,
      'to_time': toTime,
      'unit_type': unitType,
      'product_unit_type': productUnitType,
      'item_account_service_ids': itemAccountServiceIds,
      'batch_id': batchId,
      'original_price': ParseUtils.sanitizeDouble(originalPrice),
      'grand_total': ParseUtils.sanitizeDouble(grandTotal),
      'start_time': startTime,
      'end_time': endTime,
      'product_price': ParseUtils.sanitizeDouble(productPrice),
      'card_type': cardType,
      'expiration': expiration,
      'verify_otp_flg': verifyOtpFlg,
      'price_editable_flg': priceEditableFlg,
      'card_amount': ParseUtils.sanitizeDouble(cardAmount),
      'card_discount_percent': ParseUtils.sanitizeDouble(cardDiscountPercent),
      'card_decrease_commission_flg': cardDecreaseCommissionFlg,
      'created_customer_inventory_flg': createdCustomerInventoryFlg,
      'customer_treatment_card_use_flg': customerTreatmentCardUseFlg,
      'customer_treatment_card_id': customerTreatmentCardId,
      'customer_treatment_card_use_quantity': customerTreatmentCardUseQuantity,
      'print_places': printPlaces,
      'surcharge_price': ParseUtils.sanitizeDouble(surchargePrice),
      'surcharge_percent': ParseUtils.sanitizeDouble(surchargePercent),
      'surcharge_reason': surchargeReason,
      'surcharge_id': surchargeId,
      'times_printed': timesPrinted,
      'product_name_translations': productNameTranslations,
      'order_product_source_type': orderProductSourceType,
      'order_product_source_id': orderProductSourceId,
      'order_product_topping_flg': orderProductToppingFlg,
      'options': svMode ? jsonEncode(optionsData) : optionsData,
      'is_selected': isSelected,
      if (!svMode) 'combo_child': comboChild.map((child) => child.toMap()).toList(),
      'combo_price': ParseUtils.sanitizeDouble(comboPrice),
      'unit_time': unitTime,
      'unit_value': unitValue,
      'partner_warehouse_code': partnerWarehouseCode,
      'time_of_use': timeOfUse,
      'original_number': ParseUtils.sanitizeDouble(originalNumber),
      'product_code': productCode,
    };
  }

  Map<String, dynamic> toShortMap() {
    final optionsData = options.map((option) => option.toMap()).toList();
    return {
      'order_product_local_id': orderProductLocalId,
      'order_id': orderId,
      'product_id': productId,
      'shop_id': shopId,
      'product_master_id': productMasterId,
      'product_name': productName,
      'order_product_category_id': orderProductCategoryId,
      'order_product_category_name': orderProductCategoryName,
      'unit_id': unitId,
      'unit_name': unitName,
      'number': ParseUtils.sanitizeDouble(number),
      'price': ParseUtils.sanitizeDouble(price),
      'status': status,
      'product_price': ParseUtils.sanitizeDouble(productPrice),
      'discount_price': ParseUtils.sanitizeDouble(discountPrice),
      'discount_percent': ParseUtils.sanitizeDouble(discountPercent),
      'product_tax_percent': ParseUtils.sanitizeDouble(productTaxPercent),
      'product_tax_price': ParseUtils.sanitizeDouble(productTaxPrice),
      'grand_total': ParseUtils.sanitizeDouble(grandTotal),
      'print_places': printPlaces,
      'surcharge_price': ParseUtils.sanitizeDouble(surchargePrice),
      'surcharge_percent': ParseUtils.sanitizeDouble(surchargePercent),
      'options': jsonEncode(optionsData),
    };
  }

  factory OrderItem.fromMap(Map<String, dynamic> map) {
    final orderItem = OrderItem(
      orderProductLocalId: ParseUtils.getString(map['order_product_local_id'])!,
      orderId: ParseUtils.getString(map['order_id'])!,
      shopId: ParseUtils.getInt(map['shop_id'])!,
      orderProductVersionCode: ParseUtils.getInt(map['order_product_version_code']) ?? 1,
      productId: ParseUtils.getInt(map['product_id'])!,
      productMasterId: ParseUtils.getInt(map['product_master_id'])!,
      productName: ParseUtils.getString(map['product_name'])!,
      productExtendId: ParseUtils.getInt(map['product_extend_id']),
      batchNo: ParseUtils.getString(map['batch_no']),
      batchExpireDate: ParseUtils.getString(map['batch_expire_date']),
      unitId: ParseUtils.getInt(map['unit_id']),
      unitName: ParseUtils.getString(map['unit_name']),
      primaryUnitConvert: ParseUtils.getDouble(map['primary_unit_convert']),
      number: ParseUtils.getDouble(map['number']) ?? 0.0,
      price: ParseUtils.getDouble(map['price']) ?? 0.0,
      discountPrice: ParseUtils.getDouble(map['discount_price']) ?? 0.0,
      discountPercent: ParseUtils.getDouble(map['discount_percent']) ?? 0.0,
      discountReason: ParseUtils.getString(map['discount_reason']),
      discountId: ParseUtils.getInt(map['discount_id']),
      productTaxPercent: ParseUtils.getDouble(map['product_tax_percent']) ?? 0.0,
      productTaxPrice: ParseUtils.getDouble(map['product_tax_price']) ?? 0.0,
      cancelReason: ParseUtils.getString(map['cancel_reason']),
      transferFrom: ParseUtils.getString(map['transfer_from']),
      noteProduct: ParseUtils.getString(map['note_product']),
      shiftId: ParseUtils.getInt(map['shift_id']),
      paidDate: ParseUtils.getString(map['paid_date']),
      type: ParseUtils.getInt(map['type']),
      extraName: ParseUtils.getString(map['extra_name']),
      priceExtra: ParseUtils.getString(map['price_extra']),
      regdate: ParseUtils.getString(map['regdate']),
      lastUpdate: ParseUtils.getString(map['last_update']),
      status: ParseUtils.getInt(map['status']),
      orderProductStatus: ParseUtils.getInt(map['order_product_status']),
      orderProductExtraStatus: ParseUtils.getInt(map['order_product_extra_status']),
      regdateLocal: ParseUtils.getString(map['regdate_local']),
      lastUpdateLocal: ParseUtils.getString(map['last_update_local']),
      printDateLocal: ParseUtils.getString(map['print_date_local']),
      nameSortOrderFood: ParseUtils.getString(map['name_sort_order_food']),
      orderIndexOrderFood: ParseUtils.getInt(map['order_index_order_food']),
      extra: ParseUtils.getString(map['extra']),
      paidRegdate: ParseUtils.getString(map['paid_regdate']),
      orderProductInvalid: ParseUtils.getInt(map['order_product_invalid']),
      orderProductInprice: ParseUtils.getDouble(map['order_product_inprice']),
      orderProductBusinessDate: ParseUtils.getString(map['order_product_business_date']),
      promotionDetailId: ParseUtils.getInt(map['promotion_detail_id']),
      originalReflocalitem: ParseUtils.getString(map['original_reflocalitem']),
      itemextraMemo1: ParseUtils.getString(map['itemextra_memo1']),
      itemextraMemo2: ParseUtils.getString(map['itemextra_memo2']),
      pricePolicyId: ParseUtils.getInt(map['price_policy_id']),
      orderProductAccountId: ParseUtils.getInt(map['order_product_account_id']),
      orderProductAccountName: ParseUtils.getString(map['order_product_account_name']),
      orderProductCategoryId: ParseUtils.getInt(map['order_product_category_id']),
      orderProductCategoryName: ParseUtils.getString(map['order_product_category_name']),
      orderProductParentId: ParseUtils.getString(map['order_product_parent_id']),
      inventoryCreatedFlg: ParseUtils.getDouble(map['inventory_created_flg']),
      commission: ParseUtils.getString(map['commission']),
      productType: ParseUtils.getInt(map['product_type']),
      tableId: ParseUtils.getInt(map['table_id']),
      tableName: ParseUtils.getString(map['table_name']),
      areaId: ParseUtils.getInt(map['area_id']),
      areaName: ParseUtils.getString(map['area_name']),
      fromTime: ParseUtils.getString(map['from_time']),
      toTime: ParseUtils.getString(map['to_time']),
      unitType: ParseUtils.getInt(map['unit_type']),
      productUnitType: ParseUtils.getInt(map['product_unit_type']),
      itemAccountServiceIds: ParseUtils.getString(map['item_account_service_ids']),
      batchId: ParseUtils.getInt(map['batch_id']),
      originalPrice: ParseUtils.getDouble(map['original_price']),
      grandTotal: ParseUtils.getDouble(map['grand_total']) ?? 0.0,
      startTime: ParseUtils.getString(map['start_time']),
      endTime: ParseUtils.getString(map['end_time']),
      productPrice: ParseUtils.getDouble(map['product_price']) ?? 0.0,
      cardType: ParseUtils.getInt(map['card_type']),
      expiration: ParseUtils.getString(map['expiration']),
      verifyOtpFlg: ParseUtils.getInt(map['verify_otp_flg']),
      priceEditableFlg: ParseUtils.getInt(map['price_editable_flg']),
      cardAmount: ParseUtils.getDouble(map['card_amount']),
      cardDiscountPercent: ParseUtils.getDouble(map['card_discount_percent']),
      cardDecreaseCommissionFlg: ParseUtils.getInt(map['card_decrease_commission_flg']),
      createdCustomerInventoryFlg: ParseUtils.getInt(map['created_customer_inventory_flg']),
      customerTreatmentCardUseFlg: ParseUtils.getInt(map['customer_treatment_card_use_flg']),
      customerTreatmentCardId: ParseUtils.getInt(map['customer_treatment_card_id']),
      customerTreatmentCardUseQuantity: ParseUtils.getInt(map['customer_treatment_card_use_quantity']),
      printPlaces: ParseUtils.getString(map['print_places']),
      surchargePrice: ParseUtils.getDouble(map['surcharge_price']) ?? 0.0,
      surchargePercent: ParseUtils.getDouble(map['surcharge_percent']) ?? 0.0,
      surchargeReason: ParseUtils.getString(map['surcharge_reason']),
      surchargeId: ParseUtils.getInt(map['surcharge_id']),
      timesPrinted: ParseUtils.getInt(map['times_printed']),
      productNameTranslations: ParseUtils.getString(map['product_name_translations']),
      orderProductSourceType: ParseUtils.getInt(map['order_product_source_type']),
      orderProductSourceId: ParseUtils.getString(map['order_product_source_id']),
      orderProductToppingFlg: ParseUtils.getInt(map['order_product_topping_flg']),
      isSelected: ParseUtils.getBool(map['is_selected']) ?? false,
      comboChild: ParseUtils.getList(map['combo_child'], OrderItem.fromMap) ?? [],
      comboPrice: ParseUtils.getDouble(map['combo_price']) ?? 0.0,
      unitTime: ParseUtils.getInt(map['unit_time']),
      unitValue: ParseUtils.getInt(map['unit_value']),
      partnerWarehouseCode: ParseUtils.getString(map['partner_warehouse_code']),
      timeOfUse: ParseUtils.getString(map['time_of_use']),
      originalNumber: ParseUtils.getDouble(map['original_number']),
      productCode: ParseUtils.getString(map['product_code']),
    );

    final optionMaps = ParseUtils.getListMap(map['options']);

    if (optionMaps != null) {
      final options = optionMaps
          .map(
            (e) => ProductOptionItem.fromMap(
              e,
              orderItem.productId,
              orderItem.orderProductLocalId,
            ),
          )
          .toList();
      orderItem.options.assignAll(options);
    }

    return orderItem;
  }

  void updateTime({DateTime? time}) {
    final sTime = (time ?? DateTime.now()).toStringWithFormat(DateTimeFormatConstants.uiDateYmdTimehms);
    lastUpdate = sTime;
    lastUpdateLocal = sTime;
  }
}

extension OrderItemAttributeExt on OrderItem {
  int? get promotionType => ParseUtils.getInt(ParseUtils.getMap(extra)?['promotion_type']);

  String? get promotionName => ParseUtils.getString(ParseUtils.getMap(extra)?['promotion_name']);

  int? get mainPromotionId => ParseUtils.getInt(ParseUtils.getMap(extra)?["main_promotion_id"]);

  bool get isDiscount {
    final promotionId = ParseUtils.getInt(discountId);
    final percent = ParseUtils.getDouble(discountPercent);
    final price = ParseUtils.getDouble(discountPrice);
    return promotionId != 0 &&
        (promotionType == PromotionTypeEnum.discountItem.id || promotionType == PromotionTypeEnum.discountTopping.id) &&
        (percent.isNotNullOrZero || price.isNotNullOrZero);
  }

  bool get isSurcharge {
    final promotionId = ParseUtils.getInt(surchargeId);
    final percent = ParseUtils.getDouble(surchargePercent);
    final price = ParseUtils.getDouble(surchargePrice);
    return promotionId != 0 &&
        promotionType == PromotionTypeEnum.surchargeItem.id &&
        (percent.isNotNullOrZero || price.isNotNullOrZero);
  }

  bool get isValid {
    if (status == OrderStatusEnum.cancelled.id) return false;
    if (status == OrderStatusEnum.merged.id) return false;
    if (status == OrderStatusEnum.rejected.id) return false;
    return isParent;
  }

  bool get isFreeGift => promotionType == PromotionTypeEnum.freeGift.id;

  void clearDiscount() {
    discountPercent = 0;
    discountPrice = 0;
    discountReason = "";
    discountId = 0;
    extra = null;
  }

  void clearSurcharge() {
    surchargePercent = 0;
    surchargePrice = 0;
    surchargeReason = "";
    surchargeId = 0;
    extra = null;
  }

  double get grandTotalItem => (price + grandTotalTopping) * number;

  double get grandTotalTopping => options.fold(0, (total, option) => total + option.quantity * option.price);

  int getPromotionType() {
    int promotionType = -1;
    if (extra.isNullOrEmpty) return promotionType;
    final json = ParseUtils.getMap(extra) ?? {};
    promotionType = ParseUtils.getInt(json['promotion_type']) ?? -1;
    return promotionType;
  }

  bool get isParent => orderProductParentId.isNullOrEmpty || orderProductParentId == '0';

  bool get isChild => !isParent;

  String get translatedName {
    String result = productName;

    if (!productNameTranslations.isNullOrEmpty) {
      final langCode = GetIt.I<AppInfo>().languageCode.localeCode;
      final list = ParseUtils.getListMap(productNameTranslations);
      if (list != null) {
        for (final e in list) {
          final elc = ParseUtils.getString(e['langcode']);
          if (elc == null) continue;
          if (elc.toLowerCase().contains(langCode)) {
            result = ParseUtils.getString(e['text']) ?? result;
            break;
          }
        }
      }
    }

    final String? sUnitName = unitName.isNullOrEmpty ? null : '(${unitName?.toLowerCase()})';

    if (sUnitName != null) {
      if (!result.toLowerCase().contains(sUnitName)) {
        result += ' $sUnitName';
      }
    }

    return result;
  }

  bool get canDelete => status == OrderStatusEnum.unsaved.id;

  bool get canCancel => ![
        OrderStatusEnum.unsaved.id,
        OrderStatusEnum.cancelled.id,
        OrderStatusEnum.merged.id,
      ].contains(status);

  TimeServiceInfo? get timeServiceInfo => ParseUtils.fromJsonNullable(timeOfUse, TimeServiceInfo.fromJson);

  List<PrinterEnum> get getPrintPlaces {
    final printPlacesEnum = <PrinterEnum>[];
    if (printPlaces.isNotNullOrEmpty) {
      final list = printPlaces!.split('');
      for (final e in list) {
        final place = PrinterEnum.fromId(int.parse(e));
        printPlacesEnum.add(place);
      }
    }
    return printPlacesEnum;
  }
}

class TimeSession {
  final DateTime? start;
  final DateTime? end;

  TimeSession({this.start, this.end});

  factory TimeSession.fromJson(Map<String, dynamic> json) {
    return TimeSession(
      start: json['start'] != null ? DateTime.tryParse(ParseUtils.getString(json['start']) ?? '') : null,
      end: json['end'] != null ? DateTime.tryParse(ParseUtils.getString(json['end']) ?? '') : null,
    );
  }

  Map<String, dynamic> toJson() => {
        'start': start?.toIso8601String(),
        'end': end?.toIso8601String(),
      };
}

class TimeServiceInfo {
  // snapshot từ product
  final int billingBlockTime;
  final double openPrice;
  final int openPricingOnTime;
  final List<PriceAdjustment> priceAdjustments;

  // thông tin thời gian sử dụng
  final List<TimeSession> sessions;
  final List<BreakdownItem> breakdown;

  // 1 running, 0 stopped
  final int status;

  TimeServiceInfo({
    this.billingBlockTime = 0,
    this.openPrice = 0.0,
    this.openPricingOnTime = 0,
    this.priceAdjustments = const [],
    this.sessions = const [],
    this.breakdown = const [],
    this.status = 0,
  });

  TimeServiceInfo copyWith({
    int? billingBlockTime,
    double? openPrice,
    int? openPricingOnTime,
    List<PriceAdjustment>? priceAdjustments,
    List<TimeSession>? sessions,
    List<BreakdownItem>? breakdown,
    int? status,
  }) {
    return TimeServiceInfo(
      billingBlockTime: billingBlockTime ?? this.billingBlockTime,
      openPrice: openPrice ?? this.openPrice,
      openPricingOnTime: openPricingOnTime ?? this.openPricingOnTime,
      priceAdjustments: priceAdjustments ?? this.priceAdjustments,
      sessions: sessions ?? this.sessions,
      breakdown: breakdown ?? this.breakdown,
      status: status ?? this.status,
    );
  }

  factory TimeServiceInfo.fromJson(Map<String, dynamic> json) {
    return TimeServiceInfo(
      billingBlockTime: ParseUtils.getInt(json['billing_block_time']) ?? 0,
      openPrice: ParseUtils.getDouble(json['open_price']) ?? 0.0,
      openPricingOnTime: ParseUtils.getInt(json['open_pricing_on_time']) ?? 0,
      priceAdjustments: ParseUtils.getList(json['price_adjustments'], PriceAdjustment.fromJson) ?? [],
      sessions: ParseUtils.getList(json['sessions'], TimeSession.fromJson) ?? [],
      breakdown: ParseUtils.getList(json['breakdown'], BreakdownItem.fromJson) ?? [],
      status: ParseUtils.getInt(json['status']) ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'billing_block_time': billingBlockTime,
      'open_price': openPrice,
      'open_pricing_on_time': openPricingOnTime,
      'price_adjustments': priceAdjustments.map((e) => e.toJson()).toList(),
      'sessions': sessions.map((e) => e.toJson()).toList(),
      'breakdown': breakdown.map((e) => e.toJson()).toList(),
      'status': status,
    };
  }
}
