import 'dart:convert';

import 'package:common/common.dart';
import 'package:domain/dto/discharge_dto.dart';
import 'package:domain/dto/payment_method_dto.dart';
import 'package:network/network.dart';
import 'package:objectbox/objectbox.dart';
import 'package:resources/resources.dart';

import 'order_item.dart';
import 'table.dart';

@Entity()
class Order {
  @Id()
  int? id;

  @Index()
  @Unique(onConflict: ConflictStrategy.replace)
  String localId;

  String? code;
  int? status;
  int? type;
  int? channelId;
  String? channelName;
  String? note;
  int personNum;
  String? orderAccountId;
  String? orderAccountName;
  String? cashierId;
  String? cashierName;
  int? customerId;
  String? customerName;
  String? customeEmail;
  String? customerPhoneNumber;
  String? customerAddress;
  int shopId;
  int? tableId;
  String? tableName;
  int? assetId;
  int? shiftId;
  String? shiftName;
  String? shiftLocalId;
  int? promotionId;
  String? cancelReason;
  int discountId;
  double discountPrice;
  double discountPercent;
  double partnerDiscount;
  double partnerDiscountPercent;
  int? partnerDiscountCreateDebtFlg;
  String? discountReason;
  int surchargeId;
  double surchargePrice;
  double surchargePercent;
  String? surchargeReason;
  double shippingPrice;
  double taxPrice;
  double taxPercent;
  double totalPrice;
  double paidTotal;
  double total;
  double grandTotal;
  int paymentStatus;
  String? multipayMethod;
  String? deviceId;
  String? regdate;
  String? orderDate;
  String? lastUpdate;
  String? regdateLocal;
  String? lastUpdateLocal;
  String? paidDate;
  String? printDate;
  String? returnDate;
  int timesPrinted;
  int versionCode;
  int? regdateBusinessDate;
  int? businessDate;
  String? lastUpdateTimestamp;
  int? areaId;
  String? areaName;
  int? tableStatus;
  String? tax;
  int? synFlag;
  String? bookingTime;
  int? bookingTimeTimestamp;
  String? checkinTime;
  String? checkoutTime;
  String? startTime;
  String? endTime;
  String? typeName;
  String? accountServiceIds;
  int localCountFail;
  double originalPriceTotal;
  String? tip;
  double tipPrice;
  int? commissionPriceId;
  int customerTreatmentCardFlg;
  int numberOfTicketsPrinted;
  int numberOfBillsPrinted;
  String? mergedOrderId;
  int? externalAppIdentifyId;
  int? pending;
  int? typePrint;
  int? deliveryExternalAppIdentifyId;
  String? deliveryExternalServiceId;
  String? deliveryExternalServiceName;
  String? deliveryExternalOrderId;
  String? deliveryDeviceCode;
  String? orderDelivery;
  String? externalOrderId;
  String? orderLastVersion;
  double? paidDeduct;
  int? pdaSendType;
  String? notePda;
  String? orderDeviceId;
  int? paidType;
  int? notDeductBalanceSystem;
  int? publicFlag;
  String? extraDiscount;
  String? extraSurcharge;
  double? customerScanCardBalance;
  double? customerScanCardBalanceBeforePay;
  double? customerScanCardPayAmount;
  String? logOrder;
  String? action;
  int? vatFlg;
  double? toPayAmount;
  String? billInfo;
  String? sellerTaxCode;
  String? invoiceNo;
  String? searchKey;
  String? urlLookup;
  String? codeOfTax;
  String? lastUpdateSyncingFlg;
  int? createShippingPayment;
  int? autoIssueCardSelling;
  bool? isSyncSharding;
  String? customCommission;
  int? sourceId;
  int? sourceType;
  int versionCodeChangeOrder;

  final orderItems = ToMany<OrderItem>();

  String? eInvoice;

  String? codeLocal;

  @Transient()
  String? customerScanCardId;

  @Transient()
  String? exchangeGift;

  @Transient()
  int? validatePromotionFlg;

  @Transient()
  List<PrepaidCard> prepaidCards = [];

  @Transient()
  List<OrderItem> itemsForPrintKitchen = [];

  bool get isPaid => [PaymentStatusEnum.paid.id, PaymentStatusEnum.confirmed.id].contains(paymentStatus);
  bool get isTopup => note != null && note!.length > 5 && note!.substring(0, 5).contains("Topup");

  SyncFlag? get syncFlag => SyncFlag.fromId(synFlag);
  set syncFlag(SyncFlag? value) => synFlag = value?.id;

  Order({
    required this.localId,
    required this.shopId,
    this.code,
    this.status,
    this.type,
    this.channelId,
    this.channelName,
    this.note,
    this.personNum = 1,
    this.orderAccountId,
    this.orderAccountName,
    this.cashierId,
    this.cashierName,
    this.customerId,
    this.customerName,
    this.customeEmail,
    this.customerPhoneNumber,
    this.customerAddress,
    this.tableId,
    this.tableName,
    this.assetId,
    this.shiftId,
    this.shiftName,
    this.shiftLocalId,
    this.promotionId,
    this.cancelReason,
    this.discountId = 0,
    this.discountPrice = 0.0,
    this.discountPercent = 0.0,
    this.discountReason = '',
    this.partnerDiscount = 0.0,
    this.partnerDiscountPercent = 0.0,
    this.partnerDiscountCreateDebtFlg,
    this.surchargeId = 0,
    this.surchargePrice = 0.0,
    this.surchargePercent = 0.0,
    this.surchargeReason = '',
    this.shippingPrice = 0.0,
    this.taxPrice = 0.0,
    this.taxPercent = 0.0,
    this.paidTotal = 0.0,
    this.total = 0.0,
    this.totalPrice = 0.0,
    this.grandTotal = 0.0,
    this.paymentStatus = 0,
    this.multipayMethod,
    this.deviceId,
    this.regdate,
    this.lastUpdate,
    this.regdateLocal,
    this.lastUpdateLocal,
    this.paidDate,
    this.printDate,
    this.returnDate,
    this.timesPrinted = 0,
    this.versionCode = 0,
    this.regdateBusinessDate,
    this.businessDate,
    this.lastUpdateTimestamp,
    this.areaId,
    this.areaName,
    this.tableStatus,
    this.tax,
    this.synFlag,
    this.bookingTime,
    this.bookingTimeTimestamp,
    this.checkinTime,
    this.checkoutTime,
    this.typeName,
    this.accountServiceIds,
    this.orderDate,
    this.startTime,
    this.endTime,
    this.originalPriceTotal = 0.0,
    this.tip,
    this.tipPrice = 0.0,
    this.commissionPriceId,
    this.customerTreatmentCardFlg = 0,
    this.numberOfBillsPrinted = 0,
    this.numberOfTicketsPrinted = 0,
    this.mergedOrderId,
    this.localCountFail = 0,
    this.externalAppIdentifyId = 0,
    this.pending = 0,
    this.typePrint = -1,
    this.deliveryExternalAppIdentifyId = 0,
    this.deliveryExternalServiceId,
    this.deliveryExternalServiceName,
    this.deliveryExternalOrderId,
    this.deliveryDeviceCode,
    this.orderDelivery = '{}',
    this.externalOrderId = '',
    this.orderLastVersion = '',
    this.paidDeduct = 0.0,
    this.pdaSendType = 0,
    this.notePda,
    this.orderDeviceId,
    this.paidType = 0,
    this.notDeductBalanceSystem = 0,
    this.extraDiscount,
    this.extraSurcharge,
    this.customerScanCardId,
    this.publicFlag = 0,
    this.customerScanCardPayAmount = 0,
    this.customerScanCardBalanceBeforePay = 0,
    this.customerScanCardBalance = 0,
    this.vatFlg = 0,
    this.toPayAmount = 0,
    this.billInfo = '{}',
    this.logOrder = '',
    this.action = '',
    this.sellerTaxCode = '',
    this.invoiceNo = '',
    this.createShippingPayment,
    this.autoIssueCardSelling,
    this.isSyncSharding = false,
    this.customCommission = '',
    this.exchangeGift,
    this.lastUpdateSyncingFlg,
    this.urlLookup,
    this.codeOfTax,
    this.searchKey,
    this.eInvoice,
    this.versionCodeChangeOrder = 1,
    this.sourceId,
    this.sourceType,
    this.validatePromotionFlg,
    this.prepaidCards = const <PrepaidCard>[],
    this.itemsForPrintKitchen = const <OrderItem>[],
    this.codeLocal,
  });

  factory Order.fake() => Order(
        localId: '123456',
        shopId: 1,
        toPayAmount: 1234,
        code: '567890',
        tableName: 'Table 1',
        areaName: 'Area NB',
        channelName: 'Channel 10',
        regdateLocal: '2023-07-27 00:00:00',
        total: 1234,
        grandTotal: 12345,
      );

  factory Order.fromJson(String source) => Order.fromMap(json.decode(source) as Map<String, dynamic>);

  factory Order.fromMap(Map<String, dynamic> map) {
    final order = Order(
      localId: ParseUtils.getString(map['local_id'])!,
      shopId: ParseUtils.getInt(map['shop_id'])!,
      code: ParseUtils.getString(map['code']),
      status: ParseUtils.getInt(map['status']),
      type: ParseUtils.getInt(map['type']),
      channelId: ParseUtils.getInt(map['channel_id']),
      channelName: ParseUtils.getString(map['channel_name']),
      note: ParseUtils.getString(map['note']),
      personNum: ParseUtils.getInt(map['person_num']) ?? 1,
      orderAccountId: ParseUtils.getString(map['order_account_id']),
      orderAccountName: ParseUtils.getString(map['order_account_name']),
      cashierId: ParseUtils.getString(map['cashier_id']),
      cashierName: ParseUtils.getString(map['cashier_name']),
      customerId: ParseUtils.getInt(map['customer_id']),
      customerName: ParseUtils.getString(map['customer_name']),
      customeEmail: ParseUtils.getString(map['custome_email']),
      customerPhoneNumber: ParseUtils.getString(map['customer_phone_number']),
      customerAddress: ParseUtils.getString(map['customer_address']),
      tableId: ParseUtils.getInt(map['table_id']),
      tableName: ParseUtils.getString(map['table_name']),
      assetId: ParseUtils.getInt(map['asset_id']),
      shiftId: ParseUtils.getInt(map['shift_id']),
      shiftName: ParseUtils.getString(map['shift_name']),
      shiftLocalId: ParseUtils.getString(map['shift_local_id']),
      promotionId: ParseUtils.getInt(map['promotion_id']),
      cancelReason: ParseUtils.getString(map['cancel_reason']),
      discountId: ParseUtils.getInt(map['discount_id']) ?? 0,
      discountPrice: ParseUtils.getDouble(map['discount_price']) ?? 0.0,
      discountPercent: ParseUtils.getDouble(map['discount_percent']) ?? 0.0,
      partnerDiscount: ParseUtils.getDouble(map['partner_discount']) ?? 0.0,
      partnerDiscountPercent: ParseUtils.getDouble(map['partner_discount_percent']) ?? 0.0,
      partnerDiscountCreateDebtFlg: ParseUtils.getInt(map['partner_discount_create_debt_flg']),
      discountReason: ParseUtils.getString(map['discount_reason']),
      surchargeId: ParseUtils.getInt(map['surcharge_id']) ?? 0,
      surchargePrice: ParseUtils.getDouble(map['surcharge_price']) ?? 0.0,
      surchargePercent: ParseUtils.getDouble(map['surcharge_percent']) ?? 0.0,
      surchargeReason: ParseUtils.getString(map['surcharge_reason']),
      shippingPrice: ParseUtils.getDouble(map['shipping_price']) ?? 0.0,
      taxPrice: ParseUtils.getDouble(map['tax_price']) ?? 0.0,
      taxPercent: ParseUtils.getDouble(map['tax_percent']) ?? 0.0,
      paidTotal: ParseUtils.getDouble(map['paid_total']) ?? 0.0,
      total: ParseUtils.getDouble(map['total']) ?? 0.0,
      totalPrice: ParseUtils.getDouble(map['total_price']) ?? 0.0,
      grandTotal: ParseUtils.getDouble(map['grand_total']) ?? 0.0,
      paymentStatus: ParseUtils.getInt(map['payment_status']) ?? 0,
      multipayMethod: ParseUtils.getString(map['multipay_method']),
      deviceId: ParseUtils.getString(map['device_id']),
      regdate: ParseUtils.getString(map['regdate']),
      orderDate: ParseUtils.getString(map['order_date']),
      lastUpdate: ParseUtils.getString(map['last_update']),
      regdateLocal: ParseUtils.getString(map['regdate_local']),
      lastUpdateLocal: ParseUtils.getString(map['last_update_local']),
      paidDate: ParseUtils.getString(map['paid_date']),
      printDate: ParseUtils.getString(map['print_date']),
      returnDate: ParseUtils.getString(map['return_date']),
      timesPrinted: ParseUtils.getInt(map['times_printed']) ?? 0,
      versionCode: ParseUtils.getInt(map['version_code']) ?? 0,
      regdateBusinessDate: ParseUtils.getInt(map['regdate_business_date']),
      businessDate: ParseUtils.getInt(map['business_date']),
      lastUpdateTimestamp: ParseUtils.getString(map['last_update_timestamp']),
      areaId: ParseUtils.getInt(map['area_id']),
      areaName: ParseUtils.getString(map['area_name']),
      tableStatus: ParseUtils.getInt(map['table_status']),
      tax: ParseUtils.getString(map['tax']),
      synFlag: ParseUtils.getInt(map['syn_flag']),
      bookingTime: ParseUtils.getString(map['booking_time']),
      bookingTimeTimestamp: ParseUtils.getInt(map['booking_time_timestamp']),
      checkinTime: ParseUtils.getString(map['checkin_time']),
      checkoutTime: ParseUtils.getString(map['checkout_time']),
      startTime: ParseUtils.getString(map['start_time']),
      endTime: ParseUtils.getString(map['end_time']),
      typeName: ParseUtils.getString(map['type_name']),
      accountServiceIds: ParseUtils.getString(map['account_service_ids']),
      localCountFail: ParseUtils.getInt(map['local_count_fail']) ?? 0,
      originalPriceTotal: ParseUtils.getDouble(map['original_price_total']) ?? 0.0,
      tip: ParseUtils.getString(map['tip']),
      tipPrice: ParseUtils.getDouble(map['tip_price']) ?? 0.0,
      commissionPriceId: ParseUtils.getInt(map['commission_price_id']),
      customerTreatmentCardFlg: ParseUtils.getInt(map['customer_treatment_card_flg']) ?? 0,
      numberOfBillsPrinted: ParseUtils.getInt(map['number_of_bills_printed']) ?? 0,
      numberOfTicketsPrinted: ParseUtils.getInt(map['number_of_tickets_printed']) ?? 0,
      mergedOrderId: ParseUtils.getString(map['merged_order_id']),
      externalAppIdentifyId: ParseUtils.getInt(map['external_app_identify_id']),
      pending: ParseUtils.getInt(map['pending']),
      typePrint: ParseUtils.getInt(map['type_print']),
      deliveryExternalAppIdentifyId: ParseUtils.getInt(map['delivery_external_app_identify_id']),
      deliveryExternalServiceId: ParseUtils.getString(map['delivery_external_service_id']),
      deliveryExternalServiceName: ParseUtils.getString(map['delivery_external_service_name']),
      deliveryExternalOrderId: ParseUtils.getString(map['delivery_external_order_id']),
      deliveryDeviceCode: ParseUtils.getString(map['delivery_device_code']),
      orderDelivery: ParseUtils.getString(map['order_delivery']),
      externalOrderId: ParseUtils.getString(map['external_order_id']),
      orderLastVersion: ParseUtils.getString(map['order_last_version']),
      paidDeduct: ParseUtils.getDouble(map['paid_deduct']),
      pdaSendType: ParseUtils.getInt(map['pda_send_type']),
      notePda: ParseUtils.getString(map['note_pda']),
      orderDeviceId: ParseUtils.getString(map['order_device_id']),
      paidType: ParseUtils.getInt(map['paid_type']),
      notDeductBalanceSystem: ParseUtils.getInt(map['not_deduct_balance_system']),
      publicFlag: ParseUtils.getInt(map['public_flag']),
      extraDiscount: ParseUtils.getString(map['extra_discount']),
      extraSurcharge: ParseUtils.getString(map['extra_surcharge']),
      customerScanCardBalance: ParseUtils.getDouble(map['customer_scan_card_balance']),
      customerScanCardBalanceBeforePay: ParseUtils.getDouble(map['customer_scan_card_balance_before_pay']),
      customerScanCardPayAmount: ParseUtils.getDouble(map['customer_scan_card_pay_amount']),
      logOrder: ParseUtils.getString(map['log_order']),
      action: ParseUtils.getString(map['action']),
      vatFlg: ParseUtils.getInt(map['vat_flg']),
      toPayAmount: ParseUtils.getDouble(map['to_pay_amount']),
      billInfo: ParseUtils.getString(map['bill_info']),
      sellerTaxCode: ParseUtils.getString(map['seller_tax_code']),
      invoiceNo: ParseUtils.getString(map['invoice_no']),
      searchKey: ParseUtils.getString(map['search_key']),
      urlLookup: ParseUtils.getString(map['url_lookup']),
      codeOfTax: ParseUtils.getString(map['code_of_tax']),
      lastUpdateSyncingFlg: ParseUtils.getString(map['last_update_syncing_flg']),
      createShippingPayment: ParseUtils.getInt(map['create_shipping_payment']),
      autoIssueCardSelling: ParseUtils.getInt(map['auto_issue_card_selling']),
      isSyncSharding: ParseUtils.getBool(map['is_sync_sharding']),
      customCommission: ParseUtils.getString(map['custom_commission']),
      sourceType: ParseUtils.getInt(map['source_type']),
      eInvoice: ParseUtils.getString(map['e_invoice']),
      customerScanCardId: ParseUtils.getString(map['customer_scan_card_id']),
      exchangeGift: ParseUtils.getString(map['exchange_gift']),
      versionCodeChangeOrder: ParseUtils.getInt(map['version_code_change_order']) ?? 1,
      sourceId: ParseUtils.getInt(map['source_id']),
      itemsForPrintKitchen: ParseUtils.getList(map['items_for_print_kitchen'], OrderItem.fromMap) ?? [],
      codeLocal: ParseUtils.getString(map['code_local']),
    );

    final orderItemMaps = ParseUtils.getListMap(map['order_items']);
    if (orderItemMaps != null) {
      final orderItems = orderItemMaps.map((itemMap) => OrderItem.fromMap(itemMap)).toList();
      order.orderItems.addAll(orderItems);
    }

    return order;
  }

  String get displayCustomerName {
    if (customerName != null && customerName!.isNotEmpty) {
      return customerName!;
    }
    return S.current.retailCustomer;
  }

  bool get isReservation => sourceType == OrderSourceType.reservation.id;

  OrderDelivery? get getOrderDelivery => OrderDelivery.fromJson(orderDelivery);

  String getDisplayRegDate([String format = DateTimeFormatConstants.uiDateTimeWoYear]) {
    if (regdateLocal.isNullOrEmpty) {
      return '';
    }
    String? text;
    final date = DateTime.tryParse(regdateLocal ?? '');
    text = date?.toStringWithFormat(DateTimeFormatConstants.uiDateTimeWoYear);
    return text ?? '';
  }

  String get displayAreaName {
    String tableName = this.tableName ?? '';

    if (tableName.isEmpty) {
      if (type == OrderOrigin.atShop.id) {
        tableName = S.current.atCounter;
      } else if (type == OrderOrigin.partnerDelivery.id) {
        tableName = OrderOrigin.partnerDelivery.displayName;
      }
    } else {
      final areaName = this.areaName;

      if (areaName.isNotNullOrEmpty) {
        tableName = '$areaName - $tableName';
      }
    }

    return tableName;
  }

  bool isExchangeGift() {
    final billInf0 = ParseUtils.getMap(billInfo) ?? <String, dynamic>{};
    return billInf0.isNotEmpty && billInf0['exchange_infor'] != null;
  }

  void updateTime({DateTime? time, bool all = true}) {
    final sTime = (time ?? DateTime.now()).toStringWithFormat(DateTimeFormatConstants.uiDateYmdTimehms);
    if (all) {
      regdateLocal = sTime;
      orderDate = sTime;
      regdate = sTime;
      lastUpdate = sTime;
      lastUpdateLocal = sTime;
    } else {
      lastUpdate = sTime;
      lastUpdateLocal = sTime;
    }
  }

  void setTable(Table table) {
    tableId = table.tableId;
    tableName = table.name;
    areaId = table.areaId;
    areaName = table.areaName;
  }

  Order clone() {
    final order = Order(
      localId: localId,
      code: code,
      status: status,
      type: type,
      channelId: channelId,
      channelName: channelName,
      note: note,
      personNum: personNum,
      orderAccountId: orderAccountId,
      orderAccountName: orderAccountName,
      cashierId: cashierId,
      cashierName: cashierName,
      customerId: customerId,
      customerName: customerName,
      customeEmail: customeEmail,
      customerPhoneNumber: customerPhoneNumber,
      customerAddress: customerAddress,
      shopId: shopId,
      tableId: tableId,
      tableName: tableName,
      assetId: assetId,
      shiftId: shiftId,
      shiftName: shiftName,
      shiftLocalId: shiftLocalId,
      promotionId: promotionId,
      cancelReason: cancelReason,
      discountId: discountId,
      discountPrice: discountPrice,
      discountPercent: discountPercent,
      partnerDiscount: partnerDiscount,
      partnerDiscountPercent: partnerDiscountPercent,
      partnerDiscountCreateDebtFlg: partnerDiscountCreateDebtFlg,
      discountReason: discountReason,
      surchargeId: surchargeId,
      surchargePrice: surchargePrice,
      surchargePercent: surchargePercent,
      surchargeReason: surchargeReason,
      shippingPrice: shippingPrice,
      taxPrice: taxPrice,
      taxPercent: taxPercent,
      totalPrice: totalPrice,
      paidTotal: paidTotal,
      total: total,
      grandTotal: grandTotal,
      paymentStatus: paymentStatus,
      multipayMethod: multipayMethod,
      deviceId: deviceId,
      regdate: regdate,
      orderDate: orderDate,
      lastUpdate: lastUpdate,
      regdateLocal: regdateLocal,
      lastUpdateLocal: lastUpdateLocal,
      paidDate: paidDate,
      printDate: printDate,
      returnDate: returnDate,
      timesPrinted: timesPrinted,
      versionCode: versionCode,
      regdateBusinessDate: regdateBusinessDate,
      businessDate: businessDate,
      lastUpdateTimestamp: lastUpdateTimestamp,
      areaId: areaId,
      areaName: areaName,
      tableStatus: tableStatus,
      tax: tax,
      synFlag: synFlag,
      bookingTime: bookingTime,
      bookingTimeTimestamp: bookingTimeTimestamp,
      checkinTime: checkinTime,
      checkoutTime: checkoutTime,
      startTime: startTime,
      endTime: endTime,
      typeName: typeName,
      accountServiceIds: accountServiceIds,
      localCountFail: localCountFail,
      originalPriceTotal: originalPriceTotal,
      tip: tip,
      tipPrice: tipPrice,
      commissionPriceId: commissionPriceId,
      customerTreatmentCardFlg: customerTreatmentCardFlg,
      numberOfTicketsPrinted: numberOfTicketsPrinted,
      numberOfBillsPrinted: numberOfBillsPrinted,
      mergedOrderId: mergedOrderId,
      externalAppIdentifyId: externalAppIdentifyId,
      pending: pending,
      typePrint: typePrint,
      deliveryExternalAppIdentifyId: deliveryExternalAppIdentifyId,
      deliveryExternalServiceId: deliveryExternalServiceId,
      deliveryExternalServiceName: deliveryExternalServiceName,
      deliveryExternalOrderId: deliveryExternalOrderId,
      deliveryDeviceCode: deliveryDeviceCode,
      orderDelivery: orderDelivery,
      externalOrderId: externalOrderId,
      orderLastVersion: orderLastVersion,
      paidDeduct: paidDeduct,
      pdaSendType: pdaSendType,
      notePda: notePda,
      orderDeviceId: orderDeviceId,
      paidType: paidType,
      notDeductBalanceSystem: notDeductBalanceSystem,
      publicFlag: publicFlag,
      extraDiscount: extraDiscount,
      extraSurcharge: extraSurcharge,
      customerScanCardBalance: customerScanCardBalance,
      customerScanCardBalanceBeforePay: customerScanCardBalanceBeforePay,
      customerScanCardPayAmount: customerScanCardPayAmount,
      logOrder: logOrder,
      action: action,
      vatFlg: vatFlg,
      toPayAmount: toPayAmount,
      billInfo: billInfo,
      sellerTaxCode: sellerTaxCode,
      invoiceNo: invoiceNo,
      lastUpdateSyncingFlg: lastUpdateSyncingFlg,
      createShippingPayment: createShippingPayment,
      autoIssueCardSelling: autoIssueCardSelling,
      isSyncSharding: isSyncSharding,
      customCommission: customCommission,
      customerScanCardId: customerScanCardId,
      exchangeGift: exchangeGift,
      codeOfTax: codeOfTax,
      urlLookup: urlLookup,
      searchKey: searchKey,
      eInvoice: eInvoice,
      sourceId: sourceId,
      sourceType: sourceType,
      versionCodeChangeOrder: versionCodeChangeOrder,
      validatePromotionFlg: validatePromotionFlg,
      prepaidCards: prepaidCards,
      itemsForPrintKitchen: itemsForPrintKitchen.map((e) => e.clone()).toList(),
      codeLocal: codeLocal,
    );

    order.orderItems.assignAll(orderItems.map((e) => e.clone()));
    return order;
  }

  Map<String, dynamic> toMap({bool svMode = false}) {
    return {
      svMode ? 'order_local_id' : 'local_id': localId,
      'shop_id': shopId,
      'code': code,
      'status': status,
      'type': type,
      'channel_id': channelId,
      'channel_name': channelName,
      'note': note,
      'person_num': personNum,
      'order_account_id': orderAccountId,
      'order_account_name': orderAccountName,
      'cashier_id': cashierId,
      'cashier_name': cashierName,
      'customer_id': customerId,
      'customer_name': customerName,
      'custome_email': customeEmail,
      'customer_phone_number': customerPhoneNumber,
      'customer_address': customerAddress,
      'table_id': tableId,
      'table_name': tableName,
      'asset_id': assetId,
      'shift_id': shiftId,
      'shift_name': shiftName,
      'shift_local_id': shiftLocalId,
      'promotion_id': promotionId,
      'cancel_reason': cancelReason,
      'discount_id': discountId,
      'discount_price': ParseUtils.sanitizeDouble(discountPrice),
      'discount_percent': ParseUtils.sanitizeDouble(discountPercent),
      'partner_discount': ParseUtils.sanitizeDouble(partnerDiscount),
      'partner_discount_percent': ParseUtils.sanitizeDouble(partnerDiscountPercent),
      'partner_discount_create_debt_flg': partnerDiscountCreateDebtFlg,
      'discount_reason': discountReason,
      'surcharge_id': surchargeId,
      'surcharge_price': ParseUtils.sanitizeDouble(surchargePrice),
      'surcharge_percent': ParseUtils.sanitizeDouble(surchargePercent),
      'surcharge_reason': surchargeReason,
      'shipping_price': ParseUtils.sanitizeDouble(shippingPrice),
      'tax_price': ParseUtils.sanitizeDouble(taxPrice),
      'tax_percent': ParseUtils.sanitizeDouble(taxPercent),
      'total_price': ParseUtils.sanitizeDouble(totalPrice),
      'paid_total': ParseUtils.sanitizeDouble(paidTotal),
      'total': ParseUtils.sanitizeDouble(total),
      'grand_total': ParseUtils.sanitizeDouble(grandTotal),
      'payment_status': paymentStatus,
      'multipay_method': multipayMethod,
      'device_id': deviceId,
      'regdate': regdate,
      'order_date': orderDate,
      'last_update': lastUpdate,
      'regdate_local': regdateLocal,
      'last_update_local': lastUpdateLocal,
      'paid_date': paidDate,
      'print_date': printDate,
      'return_date': returnDate,
      'times_printed': timesPrinted,
      'version_code': versionCode,
      'regdate_business_date': regdateBusinessDate,
      'business_date': businessDate,
      'last_update_timestamp': lastUpdateTimestamp,
      'area_id': areaId,
      'area_name': areaName,
      'table_status': tableStatus,
      'tax': tax,
      'syn_flag': synFlag,
      'booking_time': bookingTime,
      'booking_time_timestamp': bookingTimeTimestamp,
      'checkin_time': checkinTime,
      'checkout_time': checkoutTime,
      'start_time': startTime,
      'end_time': endTime,
      'type_name': typeName,
      'account_service_ids': accountServiceIds,
      'local_count_fail': localCountFail,
      'original_price_total': ParseUtils.sanitizeDouble(originalPriceTotal),
      'tip': tip,
      'tip_price': ParseUtils.sanitizeDouble(tipPrice),
      'commission_price_id': commissionPriceId,
      'customer_treatment_card_flg': customerTreatmentCardFlg,
      'number_of_tickets_printed': numberOfTicketsPrinted,
      'number_of_bills_printed': numberOfBillsPrinted,
      'merged_order_id': mergedOrderId,
      'external_app_identify_id': externalAppIdentifyId,
      'pending': pending,
      'type_print': typePrint,
      'delivery_external_app_identify_id': deliveryExternalAppIdentifyId,
      'delivery_external_service_id': deliveryExternalServiceId,
      'delivery_external_service_name': deliveryExternalServiceName,
      'delivery_external_order_id': deliveryExternalOrderId,
      'delivery_device_code': deliveryDeviceCode,
      'order_delivery': orderDelivery,
      'external_order_id': externalOrderId,
      'order_last_version': orderLastVersion,
      'paid_deduct': ParseUtils.sanitizeDouble(paidDeduct),
      'pda_send_type': pdaSendType,
      'note_pda': notePda,
      'order_device_id': orderDeviceId,
      'paid_type': paidType,
      'not_deduct_balance_system': notDeductBalanceSystem,
      'public_flag': publicFlag,
      'extra_discount': extraDiscount,
      'extra_surcharge': extraSurcharge,
      'customer_scan_card_balance': ParseUtils.sanitizeDouble(customerScanCardBalance),
      'customer_scan_card_balance_before_pay': ParseUtils.sanitizeDouble(customerScanCardBalanceBeforePay),
      'customer_scan_card_pay_amount': ParseUtils.sanitizeDouble(customerScanCardPayAmount),
      'log_order': logOrder,
      'action': action,
      'vat_flg': vatFlg,
      'to_pay_amount': ParseUtils.sanitizeDouble(toPayAmount),
      'bill_info': billInfo,
      'seller_tax_code': sellerTaxCode,
      'invoice_no': invoiceNo,
      'search_key': searchKey,
      'url_lookup': urlLookup,
      'code_of_tax': codeOfTax,
      'last_update_syncing_flg': lastUpdateSyncingFlg,
      'create_shipping_payment': createShippingPayment,
      'auto_issue_card_selling': autoIssueCardSelling,
      'is_sync_sharding': isSyncSharding,
      'custom_commission': customCommission,
      'source_type': sourceType,
      'source_id': sourceId,
      'e_invoice': eInvoice,
      'customer_scan_card_id': customerScanCardId,
      'merged_order_ids': parseMergedOrderIds(),
      'exchange_gift': exchangeGift,
      svMode ? 'product' : 'order_items': orderItems.map((item) => item.toMap(svMode: svMode)).toList(),
      'version_code_change_order': versionCodeChangeOrder,
      'items_for_print_kitchen': itemsForPrintKitchen.map((item) => item.toMap()).toList(),
      'code_local': codeLocal,
    };
  }

  String toJson() => json.encode(toMap());

  String toSvJson() => json.encode(
        toMap(svMode: true),
      );
  String toTransactionJson() {
    return json.encode(toShortMap(svMode: true));
  }

  Map<String, dynamic> toShortMap({bool svMode = false}) {
    return {
      svMode ? 'order_local_id' : 'local_id': localId,
      'shop_id': shopId,
      'code': code,
      'discount_id': discountId,
      'discount_price': ParseUtils.sanitizeDouble(discountPrice),
      'discount_percent': ParseUtils.sanitizeDouble(discountPercent),
      'surcharge_price': ParseUtils.sanitizeDouble(surchargePrice),
      'surcharge_percent': ParseUtils.sanitizeDouble(surchargePercent),
      'tax_price': ParseUtils.sanitizeDouble(taxPrice),
      'tax_percent': ParseUtils.sanitizeDouble(taxPercent),
      'status': status,
      'total': ParseUtils.sanitizeDouble(total),
      'grand_total': ParseUtils.sanitizeDouble(grandTotal),
      svMode ? 'product' : 'order_items': orderItems.map((item) => item.toShortMap()).toList(),
      'code_local': codeLocal,
      'bill_info': billInfo,
    };
  }
}

extension OrderInfoExt on Order {
  double get totalQuanity {
    return orderItems.sumDouble((e) => e.isValid ? e.number : 0);
  }

  bool get isDoneProduct =>
      status != OrderStatusEnum.available.id &&
      status != OrderStatusEnum.cancelled.id &&
      status != OrderStatusEnum.served.id;

  bool get isPending => status == OrderStatusEnum.pending.id || status == OrderStatusEnum.pendingPickup.id;

  bool get hasAtShop => OrderOrigin.atShopIds.contains(type ?? 0) && isUserSystem;

  bool get isDelivery =>
      deliveryExternalAppIdentifyId.isNotNullOrZero || (isUserSystem && OrderOrigin.deliveryIds.contains(type ?? 0));

  String? get receiverTel => getOrderDelivery?.receiverTel;

  bool get isCod => getOrderDelivery != null && getOrderDelivery!.cod > 0;

  bool get isShippingPaid =>
      getOrderDelivery != null && getOrderDelivery!.shippingPaymentTransactionId.isNotNullOrEmpty;

  bool get isPickUp => (type == OrderOrigin.atShop.id || type == OrderOrigin.booking.id) && isUserSystem;

  bool get showPartnerInfo => partnerDiscount.isNotNullOrZero && partnerDiscountCreateDebtFlg == 0;

  bool get isCancelOrRejected => [OrderStatusEnum.cancelled.id, OrderStatusEnum.rejected.id].contains(status);

  bool get isShowDetail => isPaid || paidType == 2 || status == OrderStatusEnum.merged.id || isCancelOrRejected;

  String? get shippingAddress => getOrderDelivery?.shippingAddress;

  bool get isUserSystem => externalAppIdentifyId == ExternalAppType.userSystem.id;

  bool get isGrab => ExternalAppType.isGrab(externalAppIdentifyId);

  bool get canPrintNewOrder => !OrderStatusEnum.statusInvalid.contains(status); // dùng khi nhận push đơn mới

  bool get isPendingPayment => status == OrderStatusEnum.pendingPayment.id;
}

extension OrderExtraDiscountSurchargeExt on Order {
  bool get haveDiscountExtra => extraDiscount.isNotNullOrEmpty;
  bool get haveSurchargeExtra => extraSurcharge.isNotNullOrEmpty;

  List<DischargeDto> getExtraDiscounts() {
    if (!haveDiscountExtra) return [];
    final result = ParseUtils.getList(extraDiscount, DischargeDto.fromJson) ?? [];
    return result;
  }

  List<DischargeDto> getExtraSurcharges() {
    if (!haveSurchargeExtra) return [];
    final result = ParseUtils.getList(extraSurcharge, DischargeDto.fromJson) ?? [];
    return result;
  }

  void setExtraDiscounts(List<DischargeDto> list) {
    extraDiscount = jsonEncode(list.map((e) => e.toJson(DischargeJsonType.discount)).toList());
  }

  void setExtraSurcharges(List<DischargeDto> list) {
    extraSurcharge = jsonEncode(list.map((e) => e.toJson(DischargeJsonType.surcharge)).toList());
  }

  void addExtraDiscounts(List<DischargeDto> list, {bool manual = false}) {
    final discounts = getExtraDiscounts();

    if (manual) {
      discounts.removeWhere((e) => e.type == PromotionTypeEnum.manual.id);
      discounts.addAll(list.map((e) => e.copyWith(type: PromotionTypeEnum.manual.id)));
    } else {
      discounts.addAll(list);
    }

    setExtraDiscounts(discounts);
  }

  void addExtraSurcharges(List<DischargeDto> list, {required bool manual}) {
    final surcharges = getExtraSurcharges();

    if (manual) {
      surcharges.removeWhere((e) => e.type == PromotionTypeEnum.manual.id);
      surcharges.addAll(list.map((e) => e.copyWith(type: PromotionTypeEnum.manual.id)));
    } else {
      surcharges.addAll(list);
    }

    setExtraSurcharges(surcharges);
  }

  DischargeDto? getUnifiedManualDiscount() {
    final extraDiscounts = getExtraDiscounts();
    final manualDiscounts = extraDiscounts.where((e) => e.type == PromotionTypeEnum.manual.id).toList();

    if (manualDiscounts.isEmpty) return null;

    if (manualDiscounts.length == 1) return manualDiscounts.first;

    return DischargeDto.manual(
      type: manualDiscounts.first.type,
      isPercent: manualDiscounts.first.isPercent,
      percent: manualDiscounts.sumDouble((e) => e.percent),
      price: manualDiscounts.sumDouble((e) => e.price),
      reason: manualDiscounts.map((e) => e.reason).toList().nonNullOrEmpties.join(' '),
    );
  }

  DischargeDto? getUnifiedManualSurcharge() {
    final extraSurcharges = getExtraSurcharges();
    final manualSurcharges = extraSurcharges.where((e) => e.type == PromotionTypeEnum.manual.id).toList();

    if (manualSurcharges.isEmpty) return null;

    if (manualSurcharges.length == 1) return manualSurcharges.first;

    return DischargeDto.manual(
      type: manualSurcharges.first.type,
      isPercent: manualSurcharges.first.isPercent,
      percent: manualSurcharges.sumDouble((e) => e.percent),
      price: manualSurcharges.sumDouble((e) => e.price),
      reason: manualSurcharges.map((e) => e.reason).toList().nonNullOrEmpties.join(', '),
    );
  }
}

extension OrderNameExt on Order {
  /// có sẵn space ở đầu chuỗi ' [areaName - tableName] [code]'
  String get orderName {
    String orderName = '';
    final place = [areaName, tableName].nonNullOrEmpties.join(' - ');
    if (place.isNotEmpty) {
      orderName = ' [$place]';
    }
    if (code.isNotNullOrEmpty) {
      orderName += ' [$code]';
    }

    return orderName;
  }
}

extension OrderPaymentMethodExt on Order {
  List<PaymentMethodDto> getPaymentMethods() {
    return ParseUtils.getList(multipayMethod, PaymentMethodDto.fromJson) ?? [];
  }

  void addPaymentMethods(List<PaymentMethodDto> list) {
    final current = getPaymentMethods();
    multipayMethod = jsonEncode(current + list);
  }
}

extension OrderOriginExt on Order {
  void setAtShop() {
    type = OrderOrigin.atShop.id;
    typeName = OrderOrigin.atShop.displayName;
  }

  void setAtTable() {
    type = OrderOrigin.atTable.id;
    typeName = OrderOrigin.atTable.displayName;
  }

  bool get isTakeAway => type == OrderOrigin.partnerDelivery.id;

  void setTakeAway() {
    type = OrderOrigin.partnerDelivery.id;
    typeName = OrderOrigin.partnerDelivery.displayName;
  }
}

extension OrderBillInfoExt on Order {
  Map<String, dynamic> get mapBillInfo => ParseUtils.getMap(billInfo) ?? {};

  /// danh sách order bị gộp vào đơn này
  List<String>? parseMergedOrderIds() {
    return ParseUtils.getPrimitiveList(mapBillInfo['merge_order_ids']);
  }

  List<Table>? parseSelectedTables() {
    return ParseUtils.getList(mapBillInfo['table_selected'], Table.fromJson);
  }

  Map<String, dynamic>? parseRefundInfo() {
    return ParseUtils.getMap(mapBillInfo['refund_info']);
  }

  Map<String, dynamic> parseExchangeInfo() {
    return ParseUtils.getMap(mapBillInfo['exchange_infor']) ?? {};
  }

  void setBillInfoValue({
    Wrapped<int>? tableNumber,
    Wrapped<List<Table>>? selectedTables,
    Wrapped<TimeRange>? timeRange,
    Wrapped<List<DateTime>>? paymentDates,
    Wrapped<List<String>>? mergedOrderIds,
    Wrapped<String>? customerCode,
    Wrapped<double>? refundMoney,
    Wrapped<double>? totalPaid,
    Wrapped<Map<String, dynamic>>? refundInfo,
  }) {
    final map = mapBillInfo;

    map.addAll(
      {
        if (tableNumber != null) 'table_number': tableNumber.value,
        if (selectedTables != null)
          'table_selected':
              selectedTables.value == null ? null : jsonEncode(selectedTables.value?.map((e) => e.toJson()).toList()),
        if (timeRange != null) 'time_range': timeRange.value?.encodedString,
        if (paymentDates != null)
          'payment_date_range': paymentDates.value
              ?.map(
                (e) => e.millisecondsSinceEpoch ~/ Duration.millisecondsPerSecond,
              )
              .toList(),
        if (mergedOrderIds != null) 'merge_order_ids': mergedOrderIds.value,
        if (customerCode != null) 'customer_code': customerCode.value,
        if (refundMoney != null) 'refund_money': refundMoney.value,
        if (totalPaid != null) 'total_paid': totalPaid.value,
        if (refundInfo != null) 'refund_info': refundInfo.value,
      }..removeWhere((k, v) => v == null),
    );

    billInfo = jsonEncode(map);
  }
}

class EInvoiceInfo {
  EInvoiceInfo({
    this.eInvoiceId,
    this.batchId,
    this.externalAppId,
    this.sendMailToCustomer,
    this.orderId,
    this.status,
    this.publishType,
    this.urlLookup,
    this.searchKey,
    this.eInvoiceData,
    this.customer,
  });

  factory EInvoiceInfo.fromJson(Map<String, dynamic> json) {
    return EInvoiceInfo(
      eInvoiceId: ParseUtils.getInt(json['e_invoice_id']),
      batchId: ParseUtils.getString(json['batch_id']),
      externalAppId: ParseUtils.getString(json['external_app_id']),
      sendMailToCustomer: ParseUtils.getBool(json['send_email_to_customer']),
      orderId: ParseUtils.getString(json['order_id']),
      status: ParseUtils.getInt(json['status']),
      publishType: ParseUtils.getInt(json['publish_type']),
      urlLookup: ParseUtils.getString(json['url_lookup']),
      searchKey: ParseUtils.getString(json['search_key']),
      eInvoiceData: ParseUtils.fromJsonNullable(json['e_invoice_data'], EInvoiceData.fromJson),
      customer: ParseUtils.fromJsonNullable(json['customer'], CustomerEInvoiceInfo.fromJson),
    );
  }

  factory EInvoiceInfo.fromResPublish(Map<String, dynamic> json) {
    final eInvoiceInfo = EInvoiceInfo(
      searchKey: ParseUtils.getString(json['search_key']),
      urlLookup: ParseUtils.getString(json['url_lookup']),
      eInvoiceData: ParseUtils.fromJsonNullable(json['mtt_info'], EInvoiceData.fromResPublish),
    );
    eInvoiceInfo.codeOfTax = ParseUtils.getString(json['code_of_tax']);
    return eInvoiceInfo;
  }

  String? batchId;
  CustomerEInvoiceInfo? customer;
  EInvoiceData? eInvoiceData;
  int? eInvoiceId;
  String? externalAppId;
  String? orderId;
  int? publishType;
  String? searchKey;
  bool? sendMailToCustomer;
  int? status;
  String? urlLookup;
  String? codeOfTax;

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'e_invoice_id': eInvoiceId,
      'batch_id': batchId,
      'external_app_id': externalAppId,
      'send_email_to_customer': sendMailToCustomer,
      'order_id': orderId,
      'status': status,
      'publish_type': publishType,
      'url_lookup': urlLookup,
      'search_key': searchKey,
      'e_invoice_data': eInvoiceData?.toJson(),
      'customer': customer?.toJson(),
    };
  }
}

class EInvoiceData {
  EInvoiceData({
    this.transactionID,
    this.invNo,
    this.refID,
    this.invCode,
    this.invDate,
    this.invSeries,
    this.searchKey,
    this.codeOfTax,
    this.sellerTaxCode,
    this.invoiceNo,
  });

  factory EInvoiceData.fromJson(Map<String, dynamic> json) {
    return EInvoiceData(
      transactionID: ParseUtils.getString(json['transaction_id']),
      invNo: ParseUtils.getString(json['inv_no']),
      refID: ParseUtils.getString(json['ref_id']),
      invCode: ParseUtils.getString(json['inv_code']),
      invDate: ParseUtils.getString(json['inv_date']),
      searchKey: ParseUtils.getString(json['search_key']),
      codeOfTax: ParseUtils.getString(json['code_of_tax']),
      sellerTaxCode: ParseUtils.getString(json['seller_tax_code']),
      invoiceNo: ParseUtils.getString(json['invoice_no']),
      invSeries: ParseUtils.getString(json['inv_series']),
    );
  }

  factory EInvoiceData.fromResPublish(Map<String, dynamic> json) {
    return EInvoiceData(
      sellerTaxCode: ParseUtils.getString(json['tax_code']),
      invoiceNo: ParseUtils.getString(json['invoice_no']),
    );
  }

  String? codeOfTax;
  String? invCode;
  String? invDate;
  String? invNo;
  String? invSeries;
  String? invoiceNo;
  String? refID;
  String? searchKey;
  String? sellerTaxCode;
  String? transactionID;

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'transaction_id': transactionID,
      'inv_no': invNo,
      'ref_id': refID,
      'inv_code': invCode,
      'inv_date': invDate,
      'search_key': searchKey,
      'code_of_tax': codeOfTax,
      'seller_tax_code': sellerTaxCode,
      'invoice_no': invoiceNo,
      'inv_series': invSeries,
    };
  }
}

class CustomerEInvoiceInfo {
  CustomerEInvoiceInfo({
    this.customerId,
    this.customerName,
    this.customerEmail,
    this.customerAddress,
    this.customerTaxCode,
    this.customerPhoneNumber,
    this.customerCompanyName,
    this.customerType,
  });

  factory CustomerEInvoiceInfo.fromJson(Map<String, dynamic> json) {
    return CustomerEInvoiceInfo(
      customerId: ParseUtils.getInt(json['customer_id']),
      customerName: ParseUtils.getString(json['customer_name']),
      customerEmail: ParseUtils.getString(json['customer_email']),
      customerAddress: ParseUtils.getString(json['customer_address']),
      customerTaxCode: ParseUtils.getString(json['customer_tax_code']),
      customerPhoneNumber: ParseUtils.getString(json['customer_phone_number']),
      customerCompanyName: ParseUtils.getString(json['customer_company_name']),
      customerType: ParseUtils.getInt(json['type_customer']),
    );
  }

  String? customerAddress;
  String? customerCompanyName;
  String? customerEmail;
  int? customerId;
  String? customerName;
  String? customerPhoneNumber;
  String? customerTaxCode;
  int? customerType;

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'customer_id': customerId,
      'customer_name': customerName,
      'customer_email': customerEmail,
      'customer_address': customerAddress,
      'customer_tax_code': customerTaxCode,
      'customer_phone_number': customerPhoneNumber,
      'customer_company_name': customerCompanyName,
      'type_customer': customerType,
    };
  }
}

class OrderDelivery {
  OrderDelivery({
    required this.receiverName,
    required this.receiverTel,
    required this.shippingAddress,
    required this.cod,
    required this.shippingPaymentTransactionId,
  });

  factory OrderDelivery.fromMap(Map<String, dynamic> map) {
    return OrderDelivery(
      receiverName: ParseUtils.getString(map['receiver_name']) ?? '',
      receiverTel: ParseUtils.getString(map['receiver_tel']) ?? '',
      shippingAddress: ParseUtils.getString(map['shipping_address']) ?? '',
      cod: ParseUtils.getDouble(map['cod']) ?? 0,
      shippingPaymentTransactionId: ParseUtils.getString(map['shipping_payment_transaction_id']) ?? '',
    );
  }

  final double cod;
  final String receiverName;
  final String receiverTel;
  final String shippingAddress;
  final String shippingPaymentTransactionId;

  static OrderDelivery? fromJson(String? source) {
    return ParseUtils.fromJsonNullable(source, OrderDelivery.fromMap);
  }
}
