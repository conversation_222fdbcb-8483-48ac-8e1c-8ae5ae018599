import 'package:common/common.dart';
import 'package:network/network.dart';
import 'package:objectbox/objectbox.dart';

import 'unit_product.dart';

@Entity()
class ProductOptionItem {
  @Id()
  int? id;

  @Unique(onConflict: ConflictStrategy.replace)
  String? compositeId; // localId-parentProductId-parentOrderItemId

  final String? localId;
  final int? parentProductId;
  final String? parentOrderItemId;
  String? groupId;
  String name;
  double quantity;
  double price;
  int? productMasterId;
  int productId;
  int? unitId;
  String? unitName;
  double? unitExchange;
  double? quantitative;
  double? comboPrice;
  double? vatPercent;
  double? vatPrice;
  bool? isUpsize;
  String? code;
  bool? isProduct;
  int? editableFlg;
  int? discountId;
  double? discountPrice;
  double? discountPercent;
  String? discountReason;

  @Transient()
  List<UnitProduct>? units;

  ProductOptionItem({
    this.localId,
    this.parentProductId,
    this.parentOrderItemId,
    this.groupId,
    this.name = '',
    this.quantity = 0,
    this.price = 0,
    this.productMasterId,
    this.productId = 0,
    this.unitId,
    this.unitName,
    this.unitExchange,
    this.quantitative,
    this.comboPrice,
    this.vatPercent,
    this.vatPrice,
    this.isUpsize = false,
    this.editableFlg,
    this.code,
    this.isProduct = false,
    this.discountId,
    this.discountPrice,
    this.discountPercent,
    this.discountReason,
    this.units,
  }) : compositeId = '$localId-$parentProductId-$parentOrderItemId';

  factory ProductOptionItem.fromResponse(OptionProductResponseItem data, int parentProductId) {
    final item = ProductOptionItem(
      localId: data.localId,
      parentProductId: parentProductId,
      groupId: data.groupLocalId,
      name: data.name ?? '',
      quantity: data.quantity ?? 1,
      price: data.price ?? 0,
      productMasterId: data.productMasterId,
      productId: data.productId ?? 0,
      unitId: data.unitId,
      unitName: data.unitName,
      unitExchange: data.unitExchange,
      quantitative: data.quantitative,
      comboPrice: data.comboPrice,
      vatPercent: data.vatPercent,
      vatPrice: data.vatPrice,
      code: data.code,
      editableFlg: data.editableFlg,
      units: data.units.map(UnitProduct.fromProductMasterUnitResponse).toList(),
    );
    return item;
  }

  // Helper method to handle NaN values
  double? _sanitizeDouble(double? value) {
    if (value == null) return null;
    return value.isNaN ? 0.0 : value;
  }

  Map<String, dynamic> toMap({int? shopId}) {
    return {
      'local_id': localId,
      'group_id': groupId,
      'name': name,
      'quantity': _sanitizeDouble(quantity),
      'price': _sanitizeDouble(price),
      'product_master_id': productMasterId,
      'product_id': productId,
      'unit_id': unitId,
      'unit_name': unitName,
      'unit_exchange': _sanitizeDouble(unitExchange),
      'quantitative': _sanitizeDouble(quantitative),
      'combo_price': _sanitizeDouble(comboPrice),
      'vat_percent': _sanitizeDouble(vatPercent),
      'vat_price': _sanitizeDouble(vatPrice),
      'is_upsize': isUpsize,
      'code': code,
      'is_product': isProduct,
      'discount_id': discountId,
      'discount_price': _sanitizeDouble(discountPrice),
      'discount_percent': _sanitizeDouble(discountPercent),
      'discount_reason': discountReason,
      'editable_flg': editableFlg,
      if (units != null)
        'product_master_unit': units
            ?.map(
              (e) => e.toJson(
                shopId: shopId ?? CommonFunc.getShopId(),
                productMasterId: productMasterId,
              ),
            )
            .toList(),
    };
  }

  factory ProductOptionItem.fromMap(
    Map<String, dynamic> json,
    int parentProductId, [
    String? parentOrderItemId,
  ]) {
    return ProductOptionItem(
      localId: ParseUtils.getString(json['local_id']),
      parentProductId: parentProductId,
      parentOrderItemId: parentOrderItemId,
      groupId: ParseUtils.getString(json['group_id'] ?? json['group_local_id']),
      name: ParseUtils.getString(json['name']) ?? '',
      quantity: ParseUtils.getDouble(json['quantity']) ?? 1,
      price: ParseUtils.getDouble(json['price']) ?? 0,
      productMasterId: ParseUtils.getInt(json['product_master_id']),
      productId: ParseUtils.getInt(json['product_id']) ?? 0,
      unitId: ParseUtils.getInt(json['unit_id']),
      unitName: ParseUtils.getString(json['unit_name']),
      unitExchange: ParseUtils.getDouble(json['unit_exchange']),
      quantitative: ParseUtils.getDouble(json['quantitative']),
      comboPrice: ParseUtils.getDouble(json['combo_price']),
      vatPercent: ParseUtils.getDouble(json['vat_percent']),
      vatPrice: ParseUtils.getDouble(json['vat_price']),
      isUpsize: ParseUtils.getBool(json['is_upsize']),
      code: ParseUtils.getString(json['code']),
      discountId: ParseUtils.getInt(json['discount_id']),
      isProduct: ParseUtils.getBool(json['is_product']),
      discountPrice: ParseUtils.getDouble(json['discount_price']),
      discountPercent: ParseUtils.getDouble(json['discount_percent']),
      discountReason: ParseUtils.getString(json['discount_reason']),
      editableFlg: ParseUtils.getInt(json['editable_flg']),
    );
  }

  ProductOptionItem clone({
    int? parentProductId,
    String? parentOrderItemId,
  }) {
    final object = ProductOptionItem(
      localId: localId,
      parentProductId: parentProductId ?? this.parentProductId,
      parentOrderItemId: parentOrderItemId ?? this.parentOrderItemId,
      groupId: groupId,
      name: name,
      quantity: quantity,
      price: price,
      productMasterId: productMasterId,
      productId: productId,
      unitId: unitId,
      unitName: unitName,
      unitExchange: unitExchange,
      quantitative: quantitative,
      comboPrice: comboPrice,
      vatPercent: vatPercent,
      vatPrice: vatPrice,
      isUpsize: isUpsize,
      code: code,
      isProduct: isProduct,
      discountId: discountId,
      discountPrice: discountPrice,
      discountPercent: discountPercent,
      discountReason: discountReason,
      editableFlg: editableFlg,
    );
    object.id = id;
    object.units = units?.map((e) => e.copyWith()).toList();
    return object;
  }
}
