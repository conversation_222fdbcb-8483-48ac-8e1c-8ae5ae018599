import 'package:common/common.dart';

/// Discount / Surcharge
class DischargeDto {
  final int type;
  final String? localId;

  final bool isPercent;
  final double percent;
  final double price;
  final String? reason;
  final int applyOnOriginalPriceFlg;

  // Program (khuyến mãi/phụ thu theo chươ<PERSON> trình)
  final int? promotionId;
  final String? promotionCode;
  final int? mainPromotionId;
  final int? maxUse;
  final int? maxUseDaily;
  final double? conditionDetail;

  // Preset (thiết lập sẵn để người dùng chọn hoặc tự động áp dụng)
  final DischargeApplyType? optionApply;
  final int? assetId;
  final String? assetName;

  double get value => isPercent ? percent : price;

  const DischargeDto._({
    required this.type,
    required this.isPercent,
    required this.percent,
    required this.price,
    required this.reason,
    required this.applyOnOriginalPriceFlg,
    this.localId,
    this.promotionId,
    this.promotionCode,
    this.mainPromotionId,
    this.maxUse,
    this.maxUseDaily,
    this.conditionDetail,
    this.optionApply,
    this.assetId,
    this.assetName,
  });

  const DischargeDto.manual({
    required this.type,
    required this.isPercent,
    required this.percent,
    required this.price,
    required this.reason,
    this.localId,
    this.applyOnOriginalPriceFlg = 1,
    this.optionApply,
    this.assetId,
    this.assetName,
  })  : promotionId = null,
        promotionCode = null,
        mainPromotionId = null,
        maxUse = null,
        maxUseDaily = null,
        conditionDetail = null;

  const DischargeDto.program({
    required this.type,
    required this.isPercent,
    required this.percent,
    required this.price,
    required this.reason,
    required this.applyOnOriginalPriceFlg,
    required this.promotionId,
    this.localId,
    this.promotionCode,
    this.mainPromotionId,
    this.maxUse,
    this.maxUseDaily,
    this.conditionDetail,
  })  : optionApply = null,
        assetId = null,
        assetName = null;

  factory DischargeDto.fromJson(Map<String, dynamic> json) {
    final assetId = ParseUtils.getInt(json['asset_id']);

    double? percent = ParseUtils.getDouble(json['value_percent']);
    percent ??= ParseUtils.getDouble(json['discount_percent']);
    percent ??= ParseUtils.getDouble(json['surcharge_percent']);
    percent ??= 0;

    double? price = ParseUtils.getDouble(json['value_price']);
    price ??= ParseUtils.getDouble(json['discount_price']);
    price ??= ParseUtils.getDouble(json['surcharge_price']);
    price ??= 0;

    bool? isPercent = ParseUtils.getBool(json['is_percent']);
    isPercent ??= ParseUtils.getBool(json['is_apply_for_percent']);
    isPercent ??= percent != 0;

    String? reason = ParseUtils.getString(json['name']);
    reason ??= ParseUtils.getString(json['discount_reason']);
    reason ??= ParseUtils.getString(json['surcharge_reason']);

    return DischargeDto._(
      type: ParseUtils.getInt(json['type']) ?? -1,
      localId: ParseUtils.getString(json['local_id']) ?? '',
      isPercent: isPercent,
      percent: percent,
      price: price,
      reason: reason,
      applyOnOriginalPriceFlg: ParseUtils.getInt(json['apply_on_original_price_flg']) ?? 1,
      promotionId: ParseUtils.getInt(json['promotion_id']),
      promotionCode: ParseUtils.getString(json['promotion_code']),
      mainPromotionId: ParseUtils.getInt(json['main_promotion_id']),
      maxUse: ParseUtils.getInt(json['max_use']),
      maxUseDaily: ParseUtils.getInt(json['max_use_daily']),
      conditionDetail: ParseUtils.getDouble(json['condition_detail']),
      optionApply: DischargeApplyType.fromId(
        ParseUtils.getInt(json['option_apply']) ?? ((assetId ?? 0) > 0 ? 2 : 1),
      ),
      assetId: assetId,
      assetName: ParseUtils.getString(json['asset_name']),
    );
  }

  Map<String, dynamic> toJson(DischargeJsonType jt) {
    final result = {
      'type': type,
      'local_id': localId,
      'apply_on_original_price_flg': applyOnOriginalPriceFlg,
      'promotion_id': promotionId,
      'main_promotion_id': mainPromotionId,
      'max_use': maxUse,
      'max_use_daily': maxUseDaily,
      'condition_detail': ParseUtils.sanitizeDouble(conditionDetail),
      'asset_id': assetId,
      'asset_name': assetName,
      'option_apply': optionApply?.id,
    };

    switch (jt) {
      case DischargeJsonType.discount:
        result['is_percent'] = isPercent ? 1 : 0;
        result['discount_percent'] = ParseUtils.sanitizeDouble(percent);
        result['discount_price'] = ParseUtils.sanitizeDouble(price);
        result['discount_reason'] = reason;
        if (promotionCode != null) {
          result['promotion_code'] = promotionCode;
        }
      case DischargeJsonType.surcharge:
        result['is_percent'] = isPercent ? 1 : 0;
        result['surcharge_percent'] = ParseUtils.sanitizeDouble(percent);
        result['surcharge_price'] = ParseUtils.sanitizeDouble(price);
        result['surcharge_reason'] = reason;
      case DischargeJsonType.preset:
        result['is_apply_for_percent'] = isPercent ? 1 : 0;
        result['value_percent'] = ParseUtils.sanitizeDouble(percent);
        result['value_price'] = ParseUtils.sanitizeDouble(price);
        result['name'] = reason;
    }
    return result;
  }

  factory DischargeDto.fake() => const DischargeDto._(
        type: 0,
        localId: '',
        reason: 'tên fake',
        isPercent: false,
        percent: 0,
        price: 0,
        assetId: 0,
        assetName: 'quỹ tiền',
        promotionId: 0,
        maxUse: 0,
        maxUseDaily: 0,
        conditionDetail: 0,
        optionApply: DischargeApplyType.all,
        mainPromotionId: 0,
        applyOnOriginalPriceFlg: 1,
      );

  DischargeDto copyWith({
    int? type,
    bool? isPercent,
    double? percent,
    double? price,
    String? reason,
  }) {
    return DischargeDto._(
      type: type ?? this.type,
      localId: localId,
      isPercent: isPercent ?? this.isPercent,
      percent: percent ?? this.percent,
      price: price ?? this.price,
      reason: reason ?? this.reason,
      applyOnOriginalPriceFlg: applyOnOriginalPriceFlg,
      promotionId: promotionId,
      promotionCode: promotionCode,
      mainPromotionId: mainPromotionId,
      maxUse: maxUse,
      maxUseDaily: maxUseDaily,
      conditionDetail: conditionDetail,
      optionApply: optionApply,
      assetId: assetId,
      assetName: assetName,
    );
  }
}

enum DischargeJsonType { discount, surcharge, preset }
