import 'package:common/common.dart';
import 'package:data/data.dart';
import 'package:get_it/get_it.dart';
import 'package:injectable/injectable.dart';
import 'package:network/network.dart';

import '../../dto/shop_device_number_pos_dto.dart';
import '../login/login_use_case.dart';
import 'shop_device_params.dart';
import 'shop_device_repository.dart';

@LazySingleton()
class ShopDeviceUseCase {
  final ShopDeviceRepository shopDeviceRepository;

  const ShopDeviceUseCase(this.shopDeviceRepository);

  Future<ShopDevice> registerDevice(Map<String, dynamic> params) async {
    final result = await shopDeviceRepository.registerDevice(params);
    CommonFunc.pushSyncChanges(SyncDataType.saleDevice);
    return result;
  }

  Future<Map<int, ShopDeviceNumberPosDto>> getCurrentPosNumber(Map<String, dynamic> param) async {
    return await shopDeviceRepository.getCurrentPosNumber(param);
  }

  Future<ShopDevice> updateDevice(
    Map<String, dynamic> params, {
    required int id,
    bool ignoreSync = false,
  }) async {
    final result = await shopDeviceRepository.updateDevice(params, id: id);
    if (!ignoreSync) {
      CommonFunc.pushSyncChanges(SyncDataType.saleDevice);
    }
    return result;
  }

  Future<ShopDevice> updateIpCurrentDevice(String ip, int port) async {
    final loginDevice = CommonFunc.getLoginDevice();
    if (loginDevice == null) {
      throw Exception('Login device not found');
    }
    final ShopDeviceParams register = ShopDeviceParams(
      id: loginDevice.deviceId,
      name: loginDevice.name,
      deviceType: loginDevice.deviceType,
      deviceCode: loginDevice.deviceCode,
      kdsNotiType: loginDevice.kdsNotiType,
      serialNumber: loginDevice.serialNumber,
      ipAddress: ip,
      port: port,
      orderCodeSetting: loginDevice.orderCodeSetting,
    );

    return await updateDevice(
      register.toMap(),
      id: loginDevice.deviceId,
      ignoreSync: true,
    );
  }

  Future<List<ShopDevice>> getAllShopDevice(Map<String, dynamic> params) async {
    return await shopDeviceRepository.getAllShopDevice(params);
  }

  Future<bool> removeDevice(
    Map<String, dynamic> body, {
    required int id,
  }) async {
    final result = await shopDeviceRepository.removeDevice(body, id: id);
    CommonFunc.pushSyncChanges(SyncDataType.saleDevice);
    return result;
  }

  Future<Map<String, dynamic>> getDeviceOptions(Map<String, dynamic> params) async {
    return await shopDeviceRepository.getDeviceOptions(params);
  }

  Future<bool> updateDeviceOptions(Map<String, dynamic> params) async {
    return await shopDeviceRepository.updateDeviceOptions(params);
  }

  void insertShopDevices(List<ShopDevice> shopDevices) {
    shopDeviceRepository.insertShopDevices(shopDevices);
  }

  List<ShopDevice> loadAllShopDevices() {
    return shopDeviceRepository.loadAllShopDevices();
  }

  List<ShopDevice> loadShopDevicesByDeviceType(int deviceType) {
    return shopDeviceRepository.loadShopDevicesByDeviceType(deviceType);
  }

  void removeAllShopDevices() {
    shopDeviceRepository.removeAllShopDevices();
  }

  Future<void> syncShopDevices() async {
    final shopDevices = await getAllShopDevice({});
    removeAllShopDevices();
    insertShopDevices(shopDevices);

    final currentLoginDevice = CommonFunc.getLoginDevice();
    final currentMasterDevice = CommonFunc.getMasterDevice();

    if (currentLoginDevice != null) {
      final loginDevice =
          shopDevices.firstWhereOrNull((element) => element.deviceCode == currentLoginDevice.deviceCode);
      if (loginDevice != null) {
        final newLoginDevice = Device.fromShopDevice(loginDevice);
        newLoginDevice.isLoginDevice = true;
        GetIt.I<LoginUseCase>().clearLoginDevice();
        GetIt.I<LoginUseCase>().saveDevice(newLoginDevice);
        final hasInitOrderCode = GetIt.I<AppPreferences>().getBool(SharedPreferenceKeys.hasInitOrderCode);
        if (hasInitOrderCode == false) {
          OrderCodePool().setCurrentOrderCode(newLoginDevice.getOrderCodeSetting.totalOrder);
          GetIt.I<AppPreferences>().setBool(SharedPreferenceKeys.hasInitOrderCode, true);
        }
      }
    }

    if (currentMasterDevice != null) {
      final masterDevice =
          shopDevices.firstWhereOrNull((element) => element.deviceCode == currentMasterDevice.deviceCode);
      if (masterDevice != null) {
        final newMasterDevice = Device.fromShopDevice(masterDevice);
        newMasterDevice.isMasterDevice = true;
        GetIt.I<LoginUseCase>().clearMasterDevice();
        GetIt.I<LoginUseCase>().saveDevice(newMasterDevice);
      }
    }
  }

  Future<void> syncDeviceOptions() async {
    final deviceOptions = await getDeviceOptions({
      'device_code': CommonFunc.getLoginDevice()?.deviceCode ?? '',
      'name': 'default_product',
    });

    // TODO(Khang): Pending
  }

  Future<void> syncPackages() async {
    final response = await shopDeviceRepository.getCurrentPosNumber({});
    final data = response[CommonFunc.getShopId()];
    if (data != null && data.modules.isNotNullOrEmpty) {
      final modules = ModulesResponse.fromJson(ParseUtils.getMap(data.modules) ?? {});
      final user = UserManager().user.copyWith();
      user.modules.target = Modules(
        packageId: modules.packageId ?? AppConstants.advancePackage,
        posCashierNumber: modules.posCashierNumber ?? -1,
        posOrderNumber: modules.posOrderNumber ?? -1,
        posKdsNumber: modules.posKdsNumber ?? -1,
      );

      UserManager().user = user;
      GetIt.I<LoginUseCase>().saveUser(user);
    }
  }

  ShopDevice? loadShopDevicesByCode(String deviceCode) {
    return shopDeviceRepository.loadShopDevicesByCode(deviceCode);
  }
}
