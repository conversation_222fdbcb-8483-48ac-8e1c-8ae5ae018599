import 'dart:async';

import 'package:collection/collection.dart';
import 'package:common/common.dart';
import 'package:data/data.dart';
import 'package:injectable/injectable.dart' hide Order;

import 'processing/process_item.dart';
import 'processing/process_result.dart';

enum OrderProcessingPriority {
  fetch(5),
  saveSyncResult(7),
  ui(10),
  pda(20),
  server(30);

  final int value;
  const OrderProcessingPriority(this.value);
}

@LazySingleton()
class OrderProcessingService {
  final AppDatabase _database;

  OrderProcessingService(this._database);

  Box<OrderProcessing> get _box => _database.store.box<OrderProcessing>();

  final _queue = PriorityQueue<ProcessItem>((a, b) => a.priority.compareTo(b.priority));

  bool _isProcessing = false;
  bool _shouldStop = false;

  void clear() {
    _queue.clear();
    _box.removeAll();
  }

  // Load queue from the database
  Future<void> initialize() async {
    try {
      final orderProcessings = _box.getAll();

      _queue.addAll(
        orderProcessings.map((data) {
          final completer = Completer<ProcessResult>();
          return OrderProcessItem(data, completer);
        }),
      );
    } catch (e, s) {
      LogUtils.error(e, s, pushSentry: true);
    }

    _startProcessing();
  }

  /// Add order process to the processing queue
  /// * [priority] is defined from [OrderProcessingPriority]
  Future<ProcessResult> addOrder(
    Order order, {
    required OrderSource source,
    required OrderProcessingPriority priority,
    int syncDelay = 0,
    bool needPublishEInvoice = false,
  }) async {
    final completer = Completer<ProcessResult>();

    final orderProcessing = OrderProcessing(
      processingId: CommonFunc.randomId(),
      orderJson: order.toJson(),
      orderSource: source.value,
      priority: priority.value,
    );

    final item = OrderProcessItem(
      orderProcessing,
      completer,
      syncDelay: syncDelay,
      needPublishEInvoice: needPublishEInvoice,
    );
    return _addProcess(item);
  }

  /// Add fetch order process to the processing queue
  Future<ProcessResult> addOrderList(List<Order> orders, {OrderSource source = OrderSource.server}) async {
    final completer = Completer<ProcessResult>();
    final item = OrderListProcessItem(orders, source, completer);
    return _addProcess(item);
  }

  /// Add merge order process to the processing queue
  Future<ProcessResult> mergeOrders(
    Order sourceOrder,
    Order targetOrder, {
    required OrderSource source,
    required OrderProcessingPriority priority,
    int syncDelay = 0,
  }) async {
    final completer = Completer<ProcessResult>();

    final sourceProcessing = OrderProcessing(
      processingId: CommonFunc.randomId(),
      orderJson: sourceOrder.toJson(),
      orderSource: source.value,
      priority: priority.value,
    );

    final targetProcessing = OrderProcessing(
      processingId: CommonFunc.randomId(),
      orderJson: targetOrder.toJson(),
      orderSource: source.value,
      priority: priority.value,
    );

    final item = MergeOrderProcessItem(
      sourceProcessing,
      targetProcessing,
      completer,
      syncDelay: syncDelay,
    );

    return _addProcess(item);
  }

  Future<ProcessResult> _addProcess(ProcessItem item) {
    try {
      if (item is OrderProcessItem) {
        _box.put(item.orderProcessing);
      }
      _queue.add(item);
      _startProcessing();
    } catch (e, s) {
      LogUtils.error(e, s, pushSentry: true);
      item.completer.completeError(e);
    }

    return item.completer.future;
  }

  /// Stops the processing service
  /// Any items currently in the queue will remain and be processed when the service restarts
  void stop() {
    _shouldStop = true;
  }

  Future<void> _startProcessing() async {
    if (_isProcessing) return;

    _isProcessing = true;
    _shouldStop = false;

    while (_queue.isNotEmpty && !_shouldStop) {
      final item = _queue.removeFirst();

      try {
        final result = await item.process(this);
        item.completer.complete(result);
      } catch (error, stackTrace) {
        item.completer.completeError(error);
        final extra = <String, dynamic>{
          'priority': item.priority,
          'type': item.runtimeType.toString(),
          'order': item is OrderProcessItem ? item.orderProcessing.orderJson : null,
        };
        LogUtils.error(error, stackTrace, pushSentry: true, extra: extra);
      }

      _removeProcessedItem(item);
    }
    _isProcessing = false;
  }

  void _removeProcessedItem(ProcessItem item) {
    if (item is! OrderProcessItem) return;

    try {
      final processingId = item.orderProcessing.processingId;
      _box.query(OrderProcessing_.processingId.equals(processingId)).build().remove();
    } catch (e, s) {
      LogUtils.error(e, s, pushSentry: true);
    }
  }
}
