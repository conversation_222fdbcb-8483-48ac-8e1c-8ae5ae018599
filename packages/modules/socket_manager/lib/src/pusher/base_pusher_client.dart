import 'dart:async';

import 'package:common/common.dart';
import 'package:dart_pusher_channels/dart_pusher_channels.dart';

import '../exceptions/socket_manager_exception.dart';
import 'pusher_constants.dart';

abstract class BasePusherClient {
  PusherChannelsClient? _client;
  PusherChannelsClientLifeCycleState _currentState = PusherChannelsClientLifeCycleState.disconnected;
  StreamSubscription? _lifecycleSubscription;
  StreamSubscription? _errorSubscription;

  Future<void> setupChannels();

  Future<void> disconnect() async {
    Log.d('Disconnecting Pusher client...');
    await _lifecycleSubscription?.cancel();
    await _errorSubscription?.cancel();
    _lifecycleSubscription = null;
    _errorSubscription = null;

    try {
      await _client?.disconnect();
    } catch (e, s) {
      Log.e('Error during Pusher disconnect: $e', stackTrace: s);
    }

    _client?.dispose();
    _client = null;
    _currentState = PusherChannelsClientLifeCycleState.disconnected;
    Log.d('Pusher client disconnected.');
  }

  Future<void> initialize() async {
    if (_currentState == PusherChannelsClientLifeCycleState.pendingConnection ||
        _currentState == PusherChannelsClientLifeCycleState.reconnecting) {
      Log.e('Pusher initialization already in progress.');
      return;
    }

    Log.d('Initializing Pusher client...');
    PusherChannelsPackageLogger.enableLogs();

    final loginDevice = CommonFunc.getLoginDevice();
    if (loginDevice == null) {
      throw ConnectionException('Không tìm thấy thiết bị đăng nhập. Không thể thiết lập kết nối.'.untranslated);
    } else if (loginDevice.deviceType == POSDeviceType.posOrder.value ||
        loginDevice.deviceType == POSDeviceType.pda.value) {
      if (CommonFunc.getMasterDevice() == null) {
        throw ConnectionException(
          'Không tìm thấy thiết bị thu ngân. Vui lòng cấu hình thiết bị thu ngân trước.'.untranslated,
        );
      }
    }

    try {
      final options = PusherChannelsOptions.fromHost(
        scheme: 'wss',
        host: PusherConstants.pusherHost,
        key: PusherConstants.pusherKey,
        port: 443,
      );

      print('PusherConstants.pusherKey: ${PusherConstants.pusherKey}');
      print('PusherConstants.pusherHost: ${PusherConstants.pusherHost}');

      await _client?.disconnect().catchError((_) {});
      _client?.dispose();
      await _lifecycleSubscription?.cancel();
      await _errorSubscription?.cancel();

      _client = PusherChannelsClient.websocket(
        options: options,
        connectionErrorHandler: (exception, trace, refresh) {
          Log.e('Pusher Connection Error:', errorObject: exception, stackTrace: trace);
          _currentState = PusherChannelsClientLifeCycleState.connectionError;
        },
      );

      // Create a completer to track when connection and channel setup is complete
      final completer = Completer<void>();
      StreamSubscription? connectionSubscription;

      // Listen for the connection established event
      connectionSubscription = _client!.lifecycleStream.listen((newState) async {
        Log.d("Pusher Lifecycle during initialization: $newState (previous: $_currentState)");
        _currentState = newState;

        if (newState == PusherChannelsClientLifeCycleState.establishedConnection) {
          Log.d('Pusher Connection Established during initialization! Setting up channels...');

          try {
            // When connection is established, set up channels
            await setupChannels();

            // Complete the initialization process now that channels are set up
            if (!completer.isCompleted) {
              completer.complete();
            }
          } catch (e, s) {
            Log.e('Error during setupChannels in initialization', errorObject: e, stackTrace: s);
            if (!completer.isCompleted) {
              completer.completeError(e, s);
            }
          }
        } else if (newState == PusherChannelsClientLifeCycleState.connectionError ||
            newState == PusherChannelsClientLifeCycleState.disconnected) {
          if (!completer.isCompleted) {
            completer.completeError(ConnectionException(newState.name));
          }
        }
      });

      Timer? timeoutTimer;
      const timeout = Duration(seconds: 30);
      timeoutTimer = Timer(timeout, () {
        if (!completer.isCompleted) {
          completer.completeError(
            NetworkTimeoutException('Quá thời gian chờ kết nối'.untranslated, timeout: timeout),
          );
        }
      });

      Log.d('Connecting Pusher client...');
      await _client!.connect();

      try {
        await completer.future;
        Log.d('Pusher initialization and channel setup complete!');
      } finally {
        await connectionSubscription.cancel();
        timeoutTimer.cancel();

        _subscribeToLifecycleEvents();
        _subscribeToPusherErrors();
      }
    } on ConnectionException catch (e) {
      Log.e('Pusher connection exception', errorObject: e, stackTrace: e.stackTrace);
      _currentState = PusherChannelsClientLifeCycleState.connectionError;
      throw ConnectionException('Không thể thiết lập kết nối: ${e.message}'.untranslated);
    } catch (e, s) {
      Log.e('Pusher initialization error', errorObject: e, stackTrace: s);
      _currentState = PusherChannelsClientLifeCycleState.connectionError;
      throw ConnectionException('Không thể thiết lập kết nối: $e'.untranslated);
    }
  }

  void _subscribeToLifecycleEvents() {
    _lifecycleSubscription = _client?.lifecycleStream.listen((newState) {
      Log.d("Pusher Lifecycle: $newState (previous: $_currentState)");
      final previousState = _currentState;
      _currentState = newState;

      if (newState == PusherChannelsClientLifeCycleState.establishedConnection &&
          previousState != PusherChannelsClientLifeCycleState.establishedConnection) {
        Log.d('Pusher Connection Established! Setting up channels...');
        Future.microtask(() async {
          try {
            await setupChannels();
          } catch (e, s) {
            Log.e('Error during setupChannels', errorObject: e, stackTrace: s);
          }
        });
      }
    });
  }

  void _subscribeToPusherErrors() {
    _errorSubscription = _client?.pusherErrorEventStream.listen((event) {
      final errorData = event.tryGetDataAsMap();
      final message = errorData?['message']?.toString() ?? 'Unknown Pusher Error';
      final code = int.tryParse(errorData?['code']?.toString() ?? '');
      Log.e("Pusher Server Error: $message (Code: $code)", errorObject: errorData);
    });
  }

  Future<Channel> safeSubscribe(
    String channelName, {
    Duration timeout = const Duration(seconds: 10),
  }) async {
    if (_client == null || _currentState != PusherChannelsClientLifeCycleState.establishedConnection) {
      throw PusherSubscriptionException('Kết nối thất bại.'.untranslated);
    }

    Log.d('Subscribing to channel: $channelName');
    Channel channel;
    try {
      final isPrivate = channelName.startsWith('private-');
      final isPresence = channelName.startsWith('presence-');
      final accessToken = CommonFunc.getAccessToken();

      final authHeaders = {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Authorization': 'Bearer $accessToken',
        'Posapp-Shop-Id': CommonFunc.getShopId().toString(),
        'PosApp-Device-Code': CommonFunc.getLoginDevice()?.deviceCode ?? '',
      };

      final authEndpoint = PusherConstants.pusherAuthEndPoint;

      if (isPrivate) {
        channel = _client!.privateChannel(
          channelName,
          authorizationDelegate: EndpointAuthorizableChannelTokenAuthorizationDelegate.forPrivateChannel(
            authorizationEndpoint: authEndpoint,
            headers: authHeaders,
          ),
        );
      } else if (isPresence) {
        channel = _client!.presenceChannel(
          channelName,
          authorizationDelegate: EndpointAuthorizableChannelTokenAuthorizationDelegate.forPresenceChannel(
            authorizationEndpoint: authEndpoint,
            headers: authHeaders,
          ),
        );
      } else {
        channel = _client!.publicChannel(channelName);
      }

      channel.subscribe();
    } catch (e, s) {
      Log.e('Error subscribing to $channelName', errorObject: e, stackTrace: s);
      throw PusherSubscriptionException(
        'Kết nối thất bại. Vui lòng thử lại sau.'.untranslated,
        cause: e,
        stackTrace: s,
      );
    }

    final completer = Completer<Channel>();
    StreamSubscription? successSub;
    StreamSubscription? errorSub;

    try {
      successSub = channel.whenSubscriptionSucceeded().listen((event) {
        if (!completer.isCompleted) {
          Log.d('Subscription successful: $channelName');
          completer.complete(channel);
        }
      });

      errorSub = channel.onSubscriptionError().listen((event) {
        if (!completer.isCompleted) {
          final errorData = event.tryGetDataAsMap();
          final errorMessage =
              errorData?['error']?.toString() ?? errorData?['message']?.toString() ?? 'Unknown subscription error';
          Log.e('Subscription error for $channelName: $errorMessage', errorObject: errorData);
          completer.completeError(
            PusherSubscriptionException(
              'Kết nối thất bại. Vui lòng thử lại sau.'.untranslated,
              cause: errorData,
            ),
          );
        }
      });

      await completer.future.timeout(timeout);
      return channel;
    } on TimeoutException catch (e, s) {
      Log.e('Timeout subscribing to $channelName after $timeout');
      try {
        channel.unsubscribe();
      } catch (_) {}
      throw NetworkTimeoutException(
        'Quá thời gian chờ kết nối. Vui lòng thử lại sau.'.untranslated,
        timeout: timeout,
        cause: e,
        stackTrace: s,
      );
    } catch (e, s) {
      Log.e('Error during subscription completion for $channelName', errorObject: e, stackTrace: s);
      try {
        channel.unsubscribe();
      } catch (_) {}

      if (e is PusherSubscriptionException) {
        rethrow;
      }

      throw PusherSubscriptionException(
        'Kết nối thất bại. Vui lòng thử lại sau.'.untranslated,
        cause: e,
        stackTrace: s,
      );
    } finally {
      await successSub?.cancel();
      await errorSub?.cancel();
    }
  }

  PusherChannelsClient? get pusherClient => _client;
}
