import 'package:common/common.dart';
import 'package:data/data.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:resources/resources.dart';

import '../../model/socket_device_info.dart';
import '../../socket/pda_socket.dart';
import 'cubit/socket_client_cubit.dart';

class SocketClientPage extends StatefulWidget {
  const SocketClientPage({super.key});

  static const routeName = '/socket_client';

  @override
  State<SocketClientPage> createState() => _SocketClientPageState();
}

class _SocketClientPageState extends State<SocketClientPage> {
  final _formKey = GlobalKey<FormState>();
  late final TextEditingController _ipController;
  late final TextEditingController _portController;
  final cubit = SocketClientCubit();
  Device? masterDevice = CommonFunc.getMasterDevice();

  bool _isLoadingDevices = false;
  bool _scanningError = false;
  final List<SocketDeviceInfo> _devices = [];

  @override
  void initState() {
    super.initState();
    final pdaSocket = GetIt.I.get<PdaSocket>();
    final ip = pdaSocket.serverIp ?? masterDevice?.ipAddress;
    final port = pdaSocket.serverPort ?? masterDevice?.port ?? AppConstants.defaultPort;
    _ipController = TextEditingController(text: ip);
    _portController = TextEditingController(text: port.toString());

    // Auto scan devices when page opens if not using pusher and not connected
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final isPusher = cubit.isPusher;
      final isConnected = isPusher ? cubit.pdaPusher.isConnected.value : cubit.pdaSocket.isConnected.value;

      if (!isPusher && !isConnected) {
        _scanDevices();
      }
    });
  }

  @override
  void dispose() {
    _ipController.dispose();
    _portController.dispose();
    super.dispose();
  }

  Widget _buildDeviceList() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: Dimens.d16),
      child: _isLoadingDevices
          ? _buildLoadingState()
          : _scanningError
              ? _buildErrorState()
              : _devices.isEmpty
                  ? _buildEmptyState()
                  : _buildDeviceListView(),
    );
  }

  Widget _buildLoadingState() {
    return Padding(
      padding: const EdgeInsets.all(Dimens.d24),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AppColors.current.secondaryColor),
          ),
          const SizedBox(height: Dimens.d16),
          CText.body(
            'Đang quét thiết bị...'.untranslated,
            color: AppColors.current.primaryTextColor,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState() {
    return Padding(
      padding: const EdgeInsets.all(Dimens.d24),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(
            Icons.error_outline,
            color: Colors.red,
            size: 48,
          ),
          const SizedBox(height: Dimens.d16),
          CText.body(
            'Không thể quét thiết bị. Vui lòng thử lại.'.untranslated,
            color: AppColors.current.primaryTextColor,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Padding(
      padding: const EdgeInsets.all(Dimens.d24),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.devices,
            color: AppColors.current.secondaryColor,
            size: 48,
          ),
          const SizedBox(height: Dimens.d16),
          CText.body(
            'Không tìm thấy thiết bị thu ngân nào.'.untranslated,
            color: AppColors.current.primaryTextColor,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildDeviceListView() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        const SizedBox(height: Dimens.d8),
        ..._devices.map((device) => _buildDeviceItem(device)),
        const SizedBox(height: Dimens.d8),
      ],
    );
  }

  Widget _buildDeviceItem(SocketDeviceInfo device) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: () {
              setState(() {
                _ipController.text = device.ip;
                _portController.text = device.port.toString();
              });
            },
            child: Padding(
              padding: const EdgeInsets.symmetric(
                vertical: Dimens.d12,
                horizontal: Dimens.d16,
              ),
              child: Row(
                children: [
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: AppColors.current.secondaryColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Icon(
                      Icons.devices,
                      color: AppColors.current.secondaryColor,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: Dimens.d12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        CText.body(
                          device.deviceName,
                          color: AppColors.current.primaryTextColor,
                          fontWeight: FontWeight.w500,
                        ),
                        const SizedBox(height: Dimens.d4),
                        CText.caption(
                          'Mã thiết bị: ${device.deviceCode}'.untranslated,
                          color: AppColors.current.secondaryTextColor,
                        ),
                        CText.caption(
                          'IP: ${device.ip} - Port: ${device.port}'.untranslated,
                          color: AppColors.current.secondaryTextColor,
                        ),
                      ],
                    ),
                  ),
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 16,
                    color: AppColors.current.secondaryTextColor,
                  ),
                ],
              ),
            ),
          ),
        ),
        if (_devices.indexOf(device) < _devices.length - 1)
          const Divider(
            height: 1,
            indent: Dimens.d16,
            endIndent: Dimens.d16,
          ),
      ],
    );
  }

  Future<void> _scanDevices() async {
    setState(() {
      _scanningError = false;
      _devices.clear();
      _isLoadingDevices = true;
    });

    try {
      final pdaSocket = GetIt.I.get<PdaSocket>();
      await for (final device in pdaSocket.scanDevices()) {
        if (mounted) {
          setState(() {
            _devices.add(device);
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _scanningError = true;
        });
      }
      Log.e('Error scanning devices: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingDevices = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: cubit,
      child: ValueListenableBuilder<bool>(
        valueListenable: cubit.isPusher ? cubit.pdaPusher.isConnected : cubit.pdaSocket.isConnected,
        builder: (context, isConnected, _) {
          final isPusher = cubit.isPusher;
          return CScaffold(
            appBar: CAppBar(
              title: S.current.connectCashierMachine,
              actions: [
                CAppBarAction.label(
                  S.current.selectCashierDevice,
                  onTap: () {
                    Navigator.pushNamed(context, '/select_cashier').then((value) {
                      if (value is Device) {
                        setState(() {
                          _ipController.text = value.ipAddress ?? '';
                          _portController.text = value.port.toString();
                          masterDevice = value;
                        });
                      }
                    });
                  },
                ),
              ],
            ),
            backgroundColor: Colors.white,
            hideKeyboardWhenTouchOutside: true,
            body: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  SectionHeader(
                    title: S.current.connectionInformation,
                    trailing: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: Dimens.d12,
                        vertical: Dimens.d4,
                      ),
                      decoration: BoxDecoration(
                        color: isConnected ? Colors.green.withOpacity(0.1) : Colors.red.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(Dimens.d16),
                        border: Border.all(
                          color: isConnected ? Colors.green : Colors.red,
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            isConnected ? Icons.check_circle : Icons.error,
                            color: isConnected ? Colors.green : Colors.red,
                            size: 16,
                          ),
                          const SizedBox(width: Dimens.d4),
                          CText.caption(
                            isConnected ? S.current.connected : S.current.notConnected,
                            color: isConnected ? Colors.green : Colors.red,
                            fontWeight: FontWeight.w500,
                          ),
                        ],
                      ),
                    ),
                    backgroundColor: AppColors.current.backgroundColor,
                  ),
                  Padding(
                    padding: const EdgeInsets.all(Dimens.d16),
                    child: Form(
                      key: _formKey,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          CText.body(
                            '${'Tên thu ngân'}: ${masterDevice?.name}',
                            color: AppColors.current.secondaryTextColor,
                            fontWeight: FontWeight.w600,
                          ),
                          CText.body(
                            '${S.current.deviceCode}: ${masterDevice?.deviceCode}',
                            color: AppColors.current.secondaryTextColor,
                          ),
                          if (!isPusher) ...[
                            Column(
                              children: [
                                CTextFormField(
                                  controller: _ipController,
                                  title: S.current.ipAddress,
                                  enabled: !isConnected,
                                  validator: _validateIpAddress,
                                  keyboardType: TextInputType.number,
                                ),
                                const SizedBox(height: Dimens.d8),
                                CTextFormField(
                                  controller: _portController,
                                  title: S.current.port,
                                  enabled: !isConnected,
                                  validator: _validatePort,
                                  keyboardType: TextInputType.number,
                                ),
                              ],
                            ),
                          ],
                          const SizedBox(height: Dimens.d16),
                          CButton.custom(
                            onTap: isConnected ? cubit.disconnect : _connect,
                            backgroundColor: isConnected ? Colors.red : AppColors.current.secondaryColor,
                            labelColor: CColors.white,
                            label: isConnected ? S.current.disconnect : S.current.connect,
                          ),
                        ],
                      ),
                    ),
                  ),
                  if (!isPusher && !isConnected) ...[
                    // Only show scan button for socket connections when not connected
                    const SizedBox(height: Dimens.d12),
                    _buildScanDeviceSection(),
                  ],
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  String? _validateIpAddress(String? value) {
    if (value == null || value.isEmpty) {
      return S.current.pleaseEnterIpAddress;
    }

    if (!ValidationUtils.isIpAddress(value)) {
      return S.current.invalidIpAddress;
    }
    return null;
  }

  String? _validatePort(String? value) {
    if (value == null || value.isEmpty) {
      return S.current.pleaseEnterPort;
    }
    final port = int.tryParse(value);
    if (!ValidationUtils.isPort(port)) {
      return S.current.invalidPort;
    }
    return null;
  }

  Future<void> _connect() async {
    if (cubit.isPusher) {
      cubit.connectPusher();
    } else if (_formKey.currentState?.validate() ?? false) {
      cubit.connectSocket(_ipController.text, int.parse(_portController.text));
    }
  }

  Widget _buildScanDeviceSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Container(
          color: AppColors.current.backgroundColor,
          width: double.infinity,
          padding: const EdgeInsets.only(
            top: Dimens.d16,
            bottom: Dimens.d10,
            left: Dimens.d16,
            right: Dimens.d16,
          ),
          child: Row(
            children: [
              CText.title(
                'Thiết bị gần đây'.untranslated,
                color: AppColors.current.primaryTextColor.withOpacity(0.5),
                fontWeight: FontWeight.bold,
                maxLines: 2,
              ),
              IconButton(
                icon: const Icon(Icons.sync),
                onPressed: _scanDevices,
              ),
            ],
          ),
        ),
        _buildDeviceList(),
      ],
    );
  }
}
