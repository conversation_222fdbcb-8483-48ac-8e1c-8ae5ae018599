library login;

import 'dart:async';

import 'package:app/app.dart';
import 'package:common/common.dart';
import 'package:data/data.dart';
import 'package:domain/dto/login_local_dto.dart';
import 'package:domain/usecase/login/login_use_case.dart';
import 'package:domain/usecase/order/order_use_case.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:home/ui/home_page.dart';
import 'package:login_device/login_device.dart';
import 'package:login_device/ui/widgets/background_layer.dart';
import 'package:login_device/ui/widgets/language_selector.dart';
import 'package:login_device/ui/widgets/logo.dart';
import 'package:login_device/ui/widgets/register_button.dart';
import 'package:network/network.dart';
import 'package:resources/resources.dart';
import 'package:services/services.dart';

import 'ui/select_cashier_page.dart';

part 'bloc/login_bloc.dart';
part 'bloc/login_event.dart';
part 'bloc/login_state.dart';
part 'ui/login_container.dart';
part 'ui/login_page.dart';
part 'ui/widgets/login_by_account.dart';
part 'ui/widgets/login_by_pin.dart';

part 'ui/widgets/forgot_password_dialog.dart';
