part of '../login.dart';

class LoginBloc extends Bloc<LoginEvent, LoginState> {
  final LoginUseCase loginUseCase;
  final AppPreferences appPreferences;

  LoginBloc(this.loginUseCase, this.appPreferences) : super(const LoginState()) {
    on<LoginPageInitiated>(
      _onInitPage,
    );
    on<EyeIconPressed>(
      _onEyeIconPressed,
    );

    on<LoginTypeChanged>(
      _onChangeLoginType,
    );

    on<LoginShopCodeChanged>(
      _onChangeLoginShopCode,
    );

    on<LoginButtonPressed>(
      _onLoginButtonPressed,
    );
  }

  Future<void> _onInitPage(
    LoginPageInitiated event,
    Emitter<LoginState> emit,
  ) async {
    final loginType = appPreferences.getInt(SharedPreferenceKeys.loginType);
    if (event.isLoginAdmin) loginUseCase.clearLoginDevice();
    emit(
      state.copyWith(
        isLoginAdmin: event.isLoginAdmin,
        loginType: event.isLoginAdmin ? 1 : loginType,
      ),
    );
  }

  Future<void> _onEyeIconPressed(
    EyeIconPressed event,
    Emitter<LoginState> emit,
  ) async {
    emit(state.copyWith(obscureText: !state.obscureText));
  }

  Future<void> _onChangeLoginType(
    LoginTypeChanged event,
    Emitter<LoginState> emit,
  ) async {
    final loginType = state.loginType == 1 ? 2 : 1;
    await appPreferences.setInt(SharedPreferenceKeys.loginType, loginType);
    emit(state.copyWith(loginType: loginType));
  }

  Future<void> _onChangeLoginShopCode(
    LoginShopCodeChanged event,
    Emitter<LoginState> emit,
  ) async {
    emit(state.copyWith(isLoginShopCode: event.value));
  }

  Future<void> _onLoginButtonPressed(
    LoginButtonPressed event,
    Emitter<LoginState> emit,
  ) async {
    try {
      ViewUtils.showLoading();
      final Map<String, dynamic> userParam = {
        if (state.loginType != 2)
          if (state.isLoginShopCode) ...{
            'shop_code': event.userName,
            'pin_code': event.password,
          } else ...{
            'user': event.userName,
            'pass': event.password,
          },
        if (state.loginType == 2) 'pin_code': event.userName,
        if (state.loginType == 2 || state.isLoginShopCode) 'login_type': '1',
        if (state.loginType == 2) 'device_code': CommonFunc.getLoginDevice()?.deviceCode,
        'device_info': GetIt.I<AppInfo>().deviceInfo,
      };

      final response = await loginUseCase.userLogin(userParam);
      final shop = response.$1;

      if (shop != null) {
        if (!event.context.mounted) return;

        final Map<String, dynamic> shopParam = {
          if (state.loginType != 2)
            if (!state.isLoginShopCode) ...{
              'user': event.userName,
              'pass': event.password,
            },
          'shop_id': shop.shopId,
        };

        await loginBranch(
          event.context,
          shopParam,
          businessAreas: response.$2,
          modulesResponse: response.$3,
          accountId: shop.accountId,
          needSyncData: response.$4,
        );

        final isLoginLocal = GetIt.I<AppPreferences>().getBool(SharedPreferenceKeys.isLoginLocal) ?? false;

        if (isLoginLocal) {
          final data = LoginLocalDto(user: userParam, shop: shopParam);
          GetIt.I<AppPreferences>().setString(SharedPreferenceKeys.loginLocalData, data.encode());
        }
      } else {
        ViewUtils.closeLoading();
        await ViewUtils.showToastError(S.current.accountWrongBranch);
      }
    } catch (e, s) {
      GetIt.I<AppPreferences>().clear();
      RestApiClient.i.restoreBaseUrlSystem();
      LogUtils.error(e, s, pushSentry: true, extra: 'Login System Failed');
    } finally {
      ViewUtils.closeLoading();
    }
  }

  Future<void> loginBranch(
    BuildContext context,
    Map<String, dynamic> data, {
    int? businessAreas,
    ModulesResponse? modulesResponse,
    int? accountId,
    bool needSyncData = true,
  }) async {
    void openPage() {
      if (CommonFunc.isCashier() || CommonValue.isLoginAdmin) {
        WidgetsBinding.instance.addPostFrameCallback((_) async {
          Navigator.pushNamedAndRemoveUntil(context, HomePage.routeName, ModalRoute.withName('/'));
        });
      } else {
        WidgetsBinding.instance.addPostFrameCallback((_) async {
          final cashier = await Navigator.pushReplacementNamed(context, SelectCashierPage.routeName);
          if (cashier is Device) {
            Navigator.pushNamedAndRemoveUntil(
              CommonValue.currentContext!,
              HomePage.routeName,
              ModalRoute.withName('/'),
            );
          }
        });
      }
    }

    try {
      final loginResponse = await loginUseCase.login(data, accountId: accountId);
      if (loginResponse != null) {
        loginUseCase.saveUserLogin(loginResponse, businessAreas, modulesResponse, []);
        ViewUtils.closeLoading();

        if (!context.mounted) return;
        if (needSyncData) {
          GetIt.I<OrderUseCase>().deleteAllOrder();
          GetIt.I<OrderProcessingService>().clear();
          final pairSyncs = GetIt.I<AppBloc>().state.pairSyncs;
          CSyncDialog.open(
            context,
            pairSyncs: pairSyncs,
            isShowClose: false,
            onComplete: () => openPage(),
          );
        } else {
          openPage();
        }
      } else {
        ViewUtils.closeLoading();
        ViewUtils.showToastError(S.current.accountDoesNotExist);
      }
    } catch (e, s) {
      ViewUtils.closeLoading();
      await GetIt.I<AppPreferences>().clear();
      UserManager().clear();
      RestApiClient.i.restoreBaseUrlSystem();
      LogUtils.error(e, s, pushSentry: true, extra: 'Login Branch Failed');
    }
  }
}
