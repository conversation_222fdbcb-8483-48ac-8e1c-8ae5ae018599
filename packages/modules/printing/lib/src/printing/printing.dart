// ignore_for_file: require_trailing_commas
import 'dart:async';
import 'dart:collection';
import 'dart:typed_data';

import 'package:common/common.dart';
import 'package:data/data.dart';
import 'package:domain/dto/qr_data_dto.dart';
import 'package:domain/usecase/print_fail/print_fail_use_case.dart';
import 'package:domain/usecase/setting/setting_use_case.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:screenshot/screenshot.dart';

import '../enum/enum.dart';
import '../model/print_data.dart';
import '../model/shift_transfer_data.dart';
import '../print_template/builders/label_text_builder.dart';
import '../print_template/builders/label_widget_builder.dart';
import '../print_template/builders/print_text_builder.dart';
import '../print_template/builders/print_widget_builder.dart';
import '../print_template/builders/shift_template_builder.dart';
import '../print_template/tools/create_print_data.dart';
import '../print_template/widgets/preview_page.dart';
import '../printer/printer/base_printer.dart';
import 'print_kitchen.dart';
import 'print_kitchen_item.dart';
import 'print_kitchen_quantity.dart';
import 'printing_helper.dart';

class PrintResult {
  final bool isSuccess;

  // dùng cho in bếp
  final List<String> itemPrinted;
  final List<String> itemPrintFailed;

  PrintResult(
    this.isSuccess, {
    this.itemPrinted = const [],
    this.itemPrintFailed = const [],
  });

  @override
  String toString() =>
      'PrintResult(isSuccess: $isSuccess, itemPrinted: $itemPrinted, itemPrintFailed: $itemPrintFailed)';
}

class _PrintJob {
  final Completer<PrintResult> completer;
  final PrintData data;

  _PrintJob(this.data, this.completer);
}

class Printing {
  Printing._();
  static final Printing i = Printing._();

  final Queue<_PrintJob> _queue = Queue();
  bool _isRunning = false;

  Future<PrintResult> print(
    Order order, {
    TypePrint typePrint = TypePrint.payment,
    int typeService = 1,
    String tableTo = "",
    String orderMergeCode = "",
    QrDataDto? qrData,
    String? staffNameCancel,
    double? remainCustomerPoint,
    String? staffNameReturnItem,
  }) async {
    final completer = Completer<PrintResult>();

    final dataPrint = await createPrintData(
      order,
      typePrint: typePrint,
      typeService: typeService,
      tableTo: tableTo,
      orderMergeCode: orderMergeCode,
      qrData: qrData,
      staffNameCancel: staffNameCancel,
      remainCustomerPoint: remainCustomerPoint,
      staffNameReturnItem: staffNameReturnItem,
    );

    final printJob = _PrintJob(dataPrint, completer);
    return _addPrintJob(printJob);
  }

  Future<PrintResult> printDirect(PrintData printData) async {
    final completer = Completer<PrintResult>();

    final printJob = _PrintJob(printData, completer);
    return _addPrintJob(printJob);
  }

  Future<PrintResult> _addPrintJob(_PrintJob job) async {
    _queue.add(job);
    _startRuning();
    return job.completer.future;
  }

  Future<void> _startRuning() async {
    if (_isRunning) return;
    _isRunning = true;

    while (_queue.isNotEmpty) {
      final job = _queue.removeFirst();
      var result = PrintResult(
        false,
        itemPrintFailed: job.data.items.map((item) => item.itemLocalId).toList(),
      );

      try {
        switch (job.data.typePrint) {
          case TypePrint.payment:
          case TypePrint.provisional:
          case TypePrint.service:
          case TypePrint.mergeTable:
          case TypePrint.moveTable:
          case TypePrint.deliver:
          case TypePrint.cancelBill:
          case TypePrint.debtBill:
          case TypePrint.ticket:
          case TypePrint.exchangeGift:
          case TypePrint.qrPayment:
          case TypePrint.receiptVoucher:
          case TypePrint.paymentVoucher:
          case TypePrint.topUp:
          case TypePrint.refund:
            result = await _printBill(job);

          case TypePrint.kitchen:
          case TypePrint.onlyKitchenNotStamp:
          case TypePrint.kitchenSum:
          case TypePrint.cancelItem:
          case TypePrint.returnItem:
          case TypePrint.checkingKitchen:
            result = await _printKitchen(job);

          case TypePrint.kdsPrintKitchen:
            result = await _printKDS(job);

          case TypePrint.shiftTransfer:
            result = await _printShiftTransfer(job);

          case TypePrint.report:
          case TypePrint.receiptBill:
        }

        if (!job.completer.isCompleted) {
          job.completer.complete(result);
        }
      } catch (e) {
        if (!job.completer.isCompleted) {
          job.completer.complete(result);
        }
      }

      // Track print results for both bill and kitchen printing
      if (job.data.orderLocalId != null) {
        _trackPrintFailures(
          job.data.orderLocalId!,
          job.data.typePrint,
          result,
        );
      }

      if (result.isSuccess) {
        ViewUtils.showToastSuccess('In thành công!'.untranslated);
      } else {
        ViewUtils.showToastError('In thất bại!'.untranslated);
      }
    }

    _isRunning = false;
  }

  Future<PrintResult> _printBill(_PrintJob job) async {
    if (job.data.items.isEmpty && job.data.deposits == 0 && job.data.typePrint != TypePrint.ticket) {
      // cân nhắc chỉ nên log lại thôi, trường hợp này không in lại được
      return PrintResult(false);
    }

    final printerConfig = GetIt.I<SettingUseCase>().getPrinterByIdSetting(PrinterEnum.defaultPrinter.id);
    if (printerConfig == null || printerConfig.status == 0) {
      return PrintResult(false);
    }

    final typePrint = job.data.typePrint;
    final settingUseCase = GetIt.I<SettingUseCase>();
    final shopOption = settingUseCase.getShopOptionsLocal();
    final printOption = shopOption.printOptionDto;
    job.data.timesPrint = typePrint == TypePrint.payment ? printOption.amountBillPrintWhenPayment : 1;
    final openDrawer = printOption.isOpenCashDrawer == 1 && CommonFunc.isCashier();

    final printer = PrintingHelper.createPrinter(printerConfig);
    if (printer == null) {
      return PrintResult(false);
    }

    final result = await printer.connect();
    if (!result) {
      return PrintResult(false);
    }

    try {
      await _printWithConfig(printer, printerConfig, job.data);

      if (openDrawer) {
        await printer.openDrawer();
      }

      await printer.disconnect();
      return PrintResult(true);
    } catch (e) {
      await printer.disconnect();
      return PrintResult(false);
    }
  }

  Future<PrintResult> _printKitchen(_PrintJob job) async {
    if (job.data.items.isEmpty) {
      return PrintResult(
        false,
      );
    }

    final settingUseCase = GetIt.I<SettingUseCase>();
    final shopOption = settingUseCase.getShopOptionsLocal();
    final printOption = shopOption.printOptionDto;

    Map<PrinterConfig, List<PrintData>>? groupedData;
    final typePrint = job.data.typePrint;
    switch (typePrint) {
      case TypePrint.kitchenSum:
      case TypePrint.checkingKitchen:
      case TypePrint.returnItem:
        groupedData = PrintKitchen().handlePrintData(job.data);

      case TypePrint.kitchen:
      case TypePrint.onlyKitchenNotStamp:
      case TypePrint.cancelItem:
        if (printOption.printKitchenType == 1) {
          groupedData = PrintKitchenItem().handlePrintData(job.data);
        } else if (printOption.printKitchenType == 2) {
          groupedData = PrintKitchenQuantity().handlePrintData(job.data);
        } else {
          groupedData = PrintKitchen().handlePrintData(job.data);
        }

      default:
    }

    if (groupedData != null) {
      return await _processPrintQueues(groupedData);
    }

    return PrintResult(false);
  }

  Future<PrintResult> _printShiftTransfer(_PrintJob job) async {
    final printerConfig = GetIt.I<SettingUseCase>().getPrinterByIdSetting(PrinterEnum.defaultPrinter.id);
    if (printerConfig == null || printerConfig.status == 0) {
      return PrintResult(false);
    }

    final printer = PrintingHelper.createPrinter(printerConfig);
    if (printer == null) {
      return PrintResult(false);
    }

    final result = await printer.connect();
    if (!result) {
      return PrintResult(false);
    }

    try {
      await _printWithConfig(printer, printerConfig, job.data);

      await printer.disconnect();
      return PrintResult(true);
    } catch (e) {
      await printer.disconnect();
      return PrintResult(false);
    }
  }

  Future<PrintResult> _printKDS(_PrintJob job) async {
    final dataSaved = GetIt.I<AppPreferences>().getString(SharedPreferenceKeys.kdsPrinter);
    final printerConfig = ParseUtils.fromJsonNullable(dataSaved, PrinterConfig.fromJson);
    if (printerConfig == null || printerConfig.status == 0) {
      return PrintResult(false);
    }

    final printType = GetIt.I<AppPreferences>().getInt(SharedPreferenceKeys.kdsPrintKitchenType);
    Map<PrinterConfig, List<PrintData>> groupedData;
    if (printType == 1) {
      final printData = <PrintData>[];
      for (final item in job.data.items) {
        printData.add(
          job.data.copyWith(
            items: [item],
            timesPrint: 1,
          ),
        );
      }
      groupedData = {printerConfig: printData};
    } else if (printType == 2) {
      final printData = <PrintData>[];
      for (final item in job.data.items) {
        final quantity = item.itemQuantity.toInt();
        final newItem = item.copyWith(itemQuantity: 1);
        printData.addAll(
          List.generate(
            quantity,
            (index) => job.data.copyWith(
              items: [newItem],
              timesPrint: 1,
            ),
          ),
        );
      }
      groupedData = {printerConfig: printData};
    } else {
      groupedData = {
        printerConfig: [job.data]
      };
    }

    return await _processPrintQueues(groupedData);
  }

  Future<PrintResult> _processPrintQueues(Map<PrinterConfig, List<PrintData>> groupedData) async {
    final Set<String> idItemPrintFailed = <String>{};
    final Set<String> idItemPrinted = <String>{};

    for (final entry in groupedData.entries) {
      final printerConfig = entry.key;
      final listData = entry.value;

      final printer = PrintingHelper.createPrinter(printerConfig);
      if (printer == null) {
        final itemIds = _getAllItemLocalIdsFromPrintData(listData);
        idItemPrintFailed.addAll(itemIds);
        continue;
      }

      final result = await printer.connect();
      if (!result) {
        final itemIds = _getAllItemLocalIdsFromPrintData(listData);
        idItemPrintFailed.addAll(itemIds);
        continue;
      }

      try {
        for (final data in listData) {
          final itemIds = data.items.map((item) => item.itemLocalId).toList();
          try {
            await _printWithConfig(printer, printerConfig, data);
            idItemPrinted.addAll(itemIds);
          } catch (e) {
            idItemPrintFailed.addAll(itemIds);
          }
        }
      } finally {
        await printer.disconnect();
      }
    }

    return PrintResult(
      idItemPrintFailed.isEmpty,
      itemPrinted: idItemPrinted.toList(),
      itemPrintFailed: idItemPrintFailed.toList(),
    );
  }

  // Helper method to extract all itemLocalIds from a list of PrintData
  List<String> _getAllItemLocalIdsFromPrintData(List<PrintData> dataList) {
    final Set<String> itemIds = <String>{};
    for (final data in dataList) {
      for (final item in data.items) {
        itemIds.add(item.itemLocalId);
      }
    }
    return itemIds.toList();
  }

  Future<void> _printWithConfig(
    BasePrinter printer,
    PrinterConfig printerConfig,
    PrintData data,
  ) async {
    // Check printer config type
    final advancedConfig = printerConfig.advancedConfig;
    final isTextMode = advancedConfig.typePrint == 1 && data.typePrint.supportPrintText();

    for (var i = 0; i < data.timesPrint; i++) {
      if (!isTextMode) {
        // Image printing mode
        final image = await _generatePrintImage(data, printerConfig);
        await printer.printImage(image);
      } else {
        // Text printing mode
        final text = _generatePrintText(data, printerConfig);
        await printer.printText(text);
      }

      int restTime =
          ((printerConfig.isLabelPrinter ? advancedConfig.delayPrintSticker : advancedConfig.restTime) * 1000).round();
      if (restTime <= 0) {
        restTime = 200;
      }
      await Future.delayed(Duration(milliseconds: restTime));
    }
  }

  Future<Uint8List> _generatePrintImage(
    PrintData data,
    PrinterConfig printerConfig,
  ) async {
    final isLabelPrinter = printerConfig.isLabelPrinter;
    Widget? child;
    if (isLabelPrinter) {
      child = LabelWidgetBuilder().build(data, printerConfig);
    } else {
      switch (data.typePrint) {
        case TypePrint.shiftTransfer:
          child = ShiftTemplateBuilder().build(data as ShiftTransferData);
        default:
          child = PrintWidgetBuilder.build(data, forPrinting: printerConfig.typePaper == 2);
      }
    }

    final widget = InheritedTheme.captureAll(
      CommonValue.currentContext!,
      MediaQuery(
        data: MediaQuery.of(CommonValue.currentContext!).copyWith(
          textScaler: TextScaler.noScaling,
        ),
        child: Theme(
          data: Theme.of(CommonValue.currentContext!).copyWith(
            textTheme: Theme.of(CommonValue.currentContext!).textTheme.apply(bodyColor: Colors.black),
          ),
          child: Material(
            child: Padding(
              padding: const EdgeInsets.all(8),
              child: child,
            ),
          ),
        ),
      ),
    );

    final screenshotController = ScreenshotController();
    final pixelRatio = isLabelPrinter
        ? 1.0
        : switch (printerConfig.typePaper) {
            1 => 1.76, // 80mm
            2 => 1.2, // 58mm
            _ => 1.6, // 75mm
          };

    return screenshotController.captureFromLongWidget(
      widget,
      pixelRatio: pixelRatio,
      delay: const Duration(milliseconds: 100),
      constraints: BoxConstraints(
        maxWidth: isLabelPrinter ? double.infinity : 325.0,
      ),
    );
  }

  List<String> _generatePrintText(PrintData data, PrinterConfig config) {
    final isLabelPrinter = config.isLabelPrinter;
    if (isLabelPrinter) {
      return LabelTextBuilder(typePaper: config.typePaper).generateTemplate(data, config.advancedConfig);
    }

    return PrintTextBuilder.build(
      data,
      typePaper: config.typePaper,
    );
  }

  // Track print failures and successes
  void _trackPrintFailures(String orderId, TypePrint typePrint, PrintResult resultPrint) {
    final skipTracking = [
      TypePrint.receiptVoucher,
      TypePrint.paymentVoucher,
      TypePrint.shiftTransfer,
      TypePrint.report,
      TypePrint.receiptBill,
    ];

    if (skipTracking.contains(typePrint)) {
      return;
    }

    try {
      final printFailUseCase = GetIt.I<PrintFailUseCase>();
      final failedItems = resultPrint.itemPrintFailed;
      final successItems = resultPrint.itemPrinted;

      final isPrintBill = _isPrintBill(typePrint);

      if (isPrintBill) {
        if (resultPrint.isSuccess) {
          printFailUseCase.removePrintFail(orderId, typePrint.id);
          return;
        } else {
          printFailUseCase.savePrintFail(PrintFail(
            orderId: orderId,
            typePrint: typePrint.id,
          ));
          return;
        }
      } else {
        // Check if a PrintFail record already exists for this order and type
        final existingPrintFail = printFailUseCase.getPrintFail(orderId, typePrint.id);
        if (existingPrintFail != null) {
          // Create a new set with existing and new failed items
          final Set<String> mergedFailedItems = Set<String>.from(existingPrintFail.orderItemIds);
          mergedFailedItems.addAll(failedItems);

          // Remove successfully printed items
          for (final successItem in successItems) {
            mergedFailedItems.remove(successItem);
          }

          if (mergedFailedItems.isEmpty) {
            // If no failed items remain, remove the PrintFail record
            printFailUseCase.removePrintFail(orderId, typePrint.id);
          } else {
            // Save updated PrintFail with merged items
            final updatedPrintFail = PrintFail(
              orderId: orderId,
              typePrint: typePrint.id,
              orderItemIds: mergedFailedItems.toList(),
            );
            printFailUseCase.savePrintFail(updatedPrintFail);
          }
        } else if (failedItems.isNotEmpty) {
          // Create a new PrintFail record for the failed items
          final printFail = PrintFail(
            orderId: orderId,
            typePrint: typePrint.id,
            orderItemIds: failedItems,
          );
          printFailUseCase.savePrintFail(printFail);
        }
      }
    } catch (e, s) {
      LogUtils.error(e, s, show: false, pushSentry: true);
    }
  }

  bool _isPrintBill(TypePrint typePrint) {
    return [
      TypePrint.payment,
      TypePrint.provisional,
      TypePrint.service,
      TypePrint.mergeTable,
      TypePrint.moveTable,
      TypePrint.deliver,
      TypePrint.cancelBill,
      TypePrint.debtBill,
      TypePrint.ticket,
      TypePrint.exchangeGift
    ].contains(typePrint);
  }

  static Future<void> openPreview(
    BuildContext context,
    Order order, {
    TypePrint actionPrint = TypePrint.payment,
    int typeService = 1, // fnb normal
    String tableTo = "",
    String orderMergeCode = "",
    String? staffNameCancel,
    QrDataDto? qrData,
    double? remainCustomerPoint,
    Map<ComponentId, ComponentConfig>? customConfigs,
    String? title,
    Widget Function(Future<void> Function() onPrint, Future<Uint8List?> Function() getImageBytes)? footerBuilder,
  }) async {
    try {
      final dataPrint = await createPrintData(
        order,
        typePrint: actionPrint,
        typeService: typeService,
        tableTo: tableTo,
        orderMergeCode: orderMergeCode,
        qrData: qrData,
        staffNameCancel: staffNameCancel,
        remainCustomerPoint: remainCustomerPoint,
      );

      final contents = PrintWidgetBuilder.build(dataPrint, customConfigs: customConfigs, forPrinting: false);

      if (context.mounted) {
        await PreviewPage.show(
          context,
          contents: contents,
          title: title,
          footer: footerBuilder?.call(
            () async {
              final completer = Completer<PrintResult>();
              final printJob = _PrintJob(dataPrint, completer);
              await Printing.i._addPrintJob(printJob);
            },
            () async {
              final printerConfig = GetIt.I<SettingUseCase>().getPrinterByIdSetting(PrinterEnum.defaultPrinter.id);
              if (printerConfig == null) return null;
              final imageBytes = await Printing.i._generatePrintImage(dataPrint, printerConfig);
              return imageBytes;
            },
          ),
        );
      }

      // if (context.mounted) {
      //   await PrintTextPreview.show(
      //     context,
      //     data: dataPrint,
      //     customConfigs: customConfigs,
      //   );
      // }
    } catch (e, s) {
      LogUtils.error(e, s);
    }
  }

  static Future<void> openPreviewDirect(
    BuildContext context,
    PrintData dataPrint, {
    String? title,
    Widget Function(Future<void> Function() onPrint, Future<Uint8List?> Function() getImageBytes)? footerBuilder,
  }) async {
    try {
      final contents = PrintWidgetBuilder.build(dataPrint, forPrinting: false);

      if (context.mounted) {
        await PreviewPage.show(
          context,
          contents: contents,
          title: title,
          footer: footerBuilder?.call(
            () async {
              final completer = Completer<PrintResult>();
              final printJob = _PrintJob(dataPrint, completer);
              await Printing.i._addPrintJob(printJob);
            },
            () async {
              final printerConfig = GetIt.I<SettingUseCase>().getPrinterByIdSetting(PrinterEnum.defaultPrinter.id);
              if (printerConfig == null) return null;
              final imageBytes = await Printing.i._generatePrintImage(dataPrint, printerConfig);
              return imageBytes;
            },
          ),
        );
      }
    } catch (e, s) {
      LogUtils.error(e, s);
    }
  }
}
