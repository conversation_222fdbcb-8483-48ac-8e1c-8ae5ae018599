import 'package:common/common.dart';
import 'package:data/data.dart';
import 'package:domain/dto/discharge_dto.dart';
import 'package:domain/dto/payment_method_dto.dart';
import 'package:domain/dto/qr_data_dto.dart';
import 'package:domain/usecase/setting/setting_use_case.dart';
import 'package:domain/usecase/shop_branch/shop_branch_use_case.dart';
import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';
import 'package:resources/gen/l10n.dart';

import '../../enum/enum.dart';
import '../../model/print_data.dart';

Future<PrintData> createPrintData(
  Order order, {
  TypePrint typePrint = TypePrint.payment,
  int typeService = 1, // fnb normal
  String tableTo = "",
  String orderMergeCode = "",
  String? staffNameCancel,
  QrDataDto? qrData,
  double? remainCustomerPoint,
  String? staffNameReturnItem,
}) async {
  final cloneOrder = order.clone();
  final accountName = UserManager().user.fullName ?? '';
  final billCode = typePrint == TypePrint.moveTable ? orderMergeCode : order.code;

  if (typePrint == TypePrint.returnItem) {
    return _createPrintDataForReturnItems(order, staffNameReturnItem ?? accountName);
  }

  final shopOption = GetIt.I<SettingUseCase>().getShopOptionsLocal();
  final printOption = shopOption.printOptionDto;

  final now = DateTime.now().toIso8601String();
  final shopBranch = GetIt.I<ShopBranchUseCase>().getShopBranchByShopId(order.shopId);
  var dateFormat = 'dd-MM HH:mm';
  if (shopBranch != null) {
    dateFormat = '${shopBranch.showDateFormat!} HH:mm';
  }

  cloneOrder.orderItems.sort((a, b) {
    final aDate = DateTime.tryParse(a.regdateLocal ?? "") ?? DateTime.now();
    final bDate = DateTime.tryParse(b.regdateLocal ?? "") ?? DateTime.now();
    return aDate.millisecondsSinceEpoch.compareTo(bDate.millisecondsSinceEpoch);
  });

  cloneOrder.orderItems.removeWhere((e) => e.isChild);

  bool groupByCategory = false;
  switch (typePrint) {
    case TypePrint.payment:
    case TypePrint.provisional:
      groupByCategory = printOption.printByGroupOrderItem == 1;

    // nếu in bếp sẽ handle ở print_kitchen
    default:
  }

  final exchangeInfo = order.parseExchangeInfo();

  final items = <ItemData>[];
  int index = 0;
  double totalTaxPriceItem = 0.0;
  double totalPriceOfCancel = 0.0;
  double totalDiscountOfItems = 0.0;
  double totalSurchargeOfItems = 0.0;
  double totalOfItems = 0.0;
  String shortInfo = "";
  double refundMoney = 0.0;
  final listAnalystTaxOfProduct = <String, dynamic>{};

  for (final orderItem in cloneOrder.orderItems) {
    final isPrintMerge = typePrint == TypePrint.mergeTable;
    final isMerged = orderItem.status == OrderStatusEnum.merged.id;
    if (isPrintMerge && !isMerged) continue;

    final isPrintKitchen = typePrint == TypePrint.kitchen ||
        typePrint == TypePrint.onlyKitchenNotStamp ||
        typePrint == TypePrint.checkingKitchen;

    final isCancelled = orderItem.status == OrderStatusEnum.cancelled.id;
    final notSetPrinter = orderItem.printPlaces.isNullOrEmpty;
    if (isPrintKitchen && (isCancelled || isMerged || notSetPrinter)) continue;

    final printPlaces = _getPrintPlaces(orderItem);
    final (toppingData, totalToppingPrice) = _getToppingData(orderItem);

    final externalAppId = order.externalAppIdentifyId ?? 0;

    final price = (externalAppId > 0 && (order.channelId == 1 || order.channelId == 3))
        ? (orderItem.grandTotal - totalToppingPrice) / orderItem.number
        : orderItem.price;
    final discountPrice = orderItem.discountPrice * orderItem.number;
    final surchargePrice = orderItem.surchargePrice * orderItem.number;
    final taxPrice = orderItem.productTaxPrice * orderItem.number;

    final note = orderItem.noteProduct ?? "";
    String itemDiscountName = '';
    String itemSurchargeName = '';

    if (discountPrice > 0 && !isPrintKitchen) {
      itemDiscountName = orderItem.discountReason.isNullOrEmpty ? S.current.discount : orderItem.discountReason!;
    }
    if (surchargePrice > 0 && !isPrintKitchen) {
      itemSurchargeName = orderItem.surchargeReason.isNullOrEmpty ? S.current.surcharge : orderItem.surchargeReason!;
    }

    double totalPrice = NumberFormatUtils.roundDouble(
      (externalAppId > 0 && (order.channelId == 1 || order.channelId == 3))
          ? orderItem.grandTotal - totalToppingPrice
          : orderItem.price * orderItem.number,
    );

    final itemName = orderItem.translatedName;

    bool hasCanceled = false;
    final isPrintCancel = typePrint == TypePrint.cancelBill || typePrint == TypePrint.cancelItem;
    if (orderItem.status == OrderStatusEnum.cancelled.id && !isPrintCancel) {
      hasCanceled = true;
      totalPriceOfCancel += orderItem.grandTotal;
      totalPrice = 0;
    }

    final point = ParseUtils.getDouble(exchangeInfo[orderItem.orderProductLocalId]) ?? 0;

    ItemData? itemSaved;

    itemSaved = items.firstWhereOrNull(
      (item) =>
          item.productId == orderItem.productId &&
          item.itemPrice == price &&
          item.discountPrice == discountPrice &&
          surchargePrice == surchargePrice &&
          item.taxPrice == taxPrice &&
          item.itemQuantity == orderItem.number &&
          item.note == note &&
          item.unitId == orderItem.unitId &&
          _canMerge(item.toppingData, toppingData, true) &&
          item.productId != 0 &&
          item.hasCanceled == hasCanceled &&
          item.itemName == orderItem.productName,
    );

    if (itemSaved != null) {
      final number = itemSaved.itemQuantity + orderItem.number;
      itemSaved
        ..itemQuantity = number
        ..totalQuantity = number
        ..totalPrice += totalPrice
        ..surchargePrice += surchargePrice
        ..discountPrice += discountPrice
        ..taxPrice += taxPrice
        ..isDuplicate = true
        ..toppingData = _getListToppingDuplicate(
          itemSaved.toppingData,
          number,
          isPrintCancelProduct: typePrint == TypePrint.cancelItem,
        );

      itemSaved.grandTotal += orderItem.grandTotal;
    } else {
      final promotionType = orderItem.promotionType;
      final item = ItemData(
        itemLocalId: orderItem.orderProductLocalId,
        index: index++,
        itemName: itemName,
        itemUnitName: orderItem.unitName,
        itemQuantity: orderItem.number,
        totalQuantity: orderItem.number,
        itemPrice: price,
        discountPercent: orderItem.discountPercent,
        discountPrice: discountPrice,
        discountReason: itemDiscountName,
        totalPrice: totalPrice,
        timeStart: orderItem.fromTime,
        timeEnd: orderItem.toTime,
        printPlaces: printPlaces,
        toppingData: toppingData.map((e) => e.clone()).toList(),
        surchargePrice: surchargePrice,
        surchargePercent: orderItem.surchargePercent,
        surchargeReason: itemSurchargeName,
        note: note,
        taxName: "Thuế".untranslated,
        taxPercent: orderItem.productTaxPercent,
        taxPrice: taxPrice,
        hasCanceled: hasCanceled,
        productId: orderItem.productId,
        timesPrinted: orderItem.timesPrinted,
        orderProductCategoryId: orderItem.orderProductCategoryId,
        orderProductCategoryName: orderItem.orderProductCategoryName,
        grandTotal: orderItem.grandTotal,
        productPrice: promotionType == 10 ? orderItem.productPrice : 0,
        timeServiceInfo: orderItem.timeServiceInfo,
        point: point,
      );

      items.add(item);
    }
    if (orderItem.status != 5 || typePrint == TypePrint.cancelBill) {
      totalTaxPriceItem += orderItem.productTaxPrice * orderItem.number;
      if (orderItem.productTaxPercent > 0) {
        if (listAnalystTaxOfProduct[ParseUtils.getString(orderItem.productTaxPercent)] == null ||
            listAnalystTaxOfProduct[ParseUtils.getString(orderItem.productTaxPercent)] == 0) {
          listAnalystTaxOfProduct[ParseUtils.getString(orderItem.productTaxPercent)!] =
              orderItem.productTaxPrice * orderItem.number;
        } else {
          final currentTax = listAnalystTaxOfProduct[ParseUtils.getString(orderItem.productTaxPercent)] as double;
          final newTax = orderItem.productTaxPrice * orderItem.number;
          listAnalystTaxOfProduct[ParseUtils.getString(orderItem.productTaxPercent)!] = currentTax + newTax;
        }
      }

      totalDiscountOfItems += discountPrice;
      totalSurchargeOfItems += surchargePrice;
      totalOfItems += orderItem.number;
    }
  }

  if (groupByCategory) {
    // Group items by category
    final itemsByCategory = items.groupBy((e) => e.orderProductCategoryName!);
    items.clear();

    // Sort categories and add items with category headers
    final sortedCategories = itemsByCategory.keys.toList()..sort();
    int index = 0;
    for (final category in sortedCategories) {
      index++;
      items.add(ItemData.category('$index. $category'));
      items.addAll(itemsByCategory[category]!);
    }
  }

  final paymentMethods = <PaymentMethodData>[];
  String payDate = '';
  int indexDeposit = 0;
  double depositsPrice = 0;
  final listDeposits = <DepositData>[];

  if (order.multipayMethod.isNotNullOrEmpty) {
    final multipayMethod = ParseUtils.getList(order.multipayMethod, PaymentMethodDto.fromJson);
    if (multipayMethod?.isNotEmpty == true) {
      for (final e in multipayMethod!) {
        if (e.pay > 0) {
          if (order.isReservation) {
            if (e.paydate != payDate && e.paydate != null) {
              payDate = e.paydate ?? '';
              indexDeposit += 1;
            }
            depositsPrice += e.pay;
            final indexDepositStr = '${S.current.times} $indexDeposit';

            listDeposits.add(
              DepositData(
                nameWallet: e.assetName,
                deposit: e.pay,
                index: indexDepositStr,
              ),
            );
          }
          paymentMethods.add(
            PaymentMethodData(
              nameWallet: e.assetName,
              value: e.pay,
              quantity: ParseUtils.getInt(e.amount)!,
            ),
          );
        }
      }
    }
  }

  final startTime = order.checkinTime ?? '';

  String tableName = order.tableName ?? '';

  if (tableName.isEmpty) {
    if (order.type == 1) {
      tableName = S.current.atCounter;
    } else if (order.type == 5) {
      tableName = S.current.takeAway;
    }
  } else {
    final areaName = order.areaName;

    if (areaName.isNotNullOrEmpty) {
      tableName = '$areaName - $tableName';
    }
  }

  String timeCreated = order.orderDate ?? '';

  if (typePrint == TypePrint.moveTable || typePrint == TypePrint.mergeTable) {
    timeCreated = order.lastUpdateLocal ?? '';
  }

  String staffName = order.orderItems.isNotEmpty ? order.orderItems.last.orderProductAccountName ?? "" : "";

  if (typePrint == TypePrint.moveTable || typePrint == TypePrint.mergeTable) {
    staffName = UserManager().user.fullName ?? "";
  }

  final noteBill = order.note ?? '';
  final cancelReason = order.cancelReason ?? "";
  String customerAddress = order.customerAddress ?? "";
  String customerName = order.customerName ?? '';
  String customerPhone = order.customerPhoneNumber ?? '';
  final deliveryExternalId = order.deliveryExternalAppIdentifyId ?? 0;
  final externalId = order.externalAppIdentifyId ?? 0;

  if ((typePrint == TypePrint.deliver || externalId == 2 || externalId == 8 || deliveryExternalId > 0) &&
      (order.type == 4 || order.type == 5)) {
    if (order.orderDelivery.isNotNullOrEmpty) {
      final json = ParseUtils.getMap(order.orderDelivery) ?? {};

      if (json.isNotEmpty) {
        final orderDelivery = DeliveryModel.fromJson(json);
        customerAddress = orderDelivery.shippingAddress;
        customerName = orderDelivery.receiverName;
        customerPhone = orderDelivery.receiverTel;
      }
    }
  }

  String timeStart = "";
  String timeEnd = "";

  if (timeCreated.isNotEmpty) {
    timeStart = timeCreated;
  }

  if (order.multipayMethod.isNotNullOrEmpty) {
    timeEnd = order.checkoutTime ?? '';
  } else {
    if (order.lastUpdateLocal.isNotNullOrEmpty) {
      timeEnd = order.lastUpdateLocal!;
    }
  }

  // Xử lý áp dụng nhiều khuyến mãi
  final discounts = <DiscountSurchargeData>[];
  final double total = order.total;
  if (order.discountPercent.isNotNullOrZero && order.discountPercent == 100 ||
      (order.discountPrice.isNotNullOrZero && order.discountPrice == total)) {
    discounts.add(
      DiscountSurchargeData(
        percent: 100,
        price: order.discountPrice,
      ),
    );
  } else {
    if (order.haveDiscountExtra) {
      for (final DischargeDto d in order.getExtraDiscounts()) {
        discounts.add(
          DiscountSurchargeData(
            percent: d.isPercent ? d.percent : 0,
            price: d.price,
            name: d.reason ?? '',
          ),
        );
      }
    } else {
      if (order.discountPrice > 0) {
        discounts.add(
          DiscountSurchargeData(
            percent: order.discountPercent,
            price: order.discountPrice,
            name: order.discountReason ?? '',
          ),
        );
      }
    }
  }

  // Xử lý ap dụng nhiều phụ thu
  final surcharges = <DiscountSurchargeData>[];
  if (order.haveSurchargeExtra) {
    for (final DischargeDto d in order.getExtraSurcharges()) {
      surcharges.add(
        DiscountSurchargeData(
          percent: d.isPercent ? d.percent : 0,
          price: d.price,
          name: d.reason ?? '',
        ),
      );
    }
  } else {
    if (order.surchargePrice > 0) {
      surcharges.add(
        DiscountSurchargeData(
          price: order.surchargePrice,
          percent: order.surchargePercent,
          name: order.surchargeReason ?? '',
        ),
      );
    }
  }

  if (order.billInfo.isNotNullOrEmpty) {
    try {
      final data = ParseUtils.getMap(order.billInfo) ?? {};
      // ignore: parameter_assignments
      refundMoney = ParseUtils.getDouble(data["refund_money"]) ?? 0.0;
    } catch (_) {}
  }

  double totalTaxPrice = 0;

  if (!printOption.totalPriceIncludesTax) {
    try {
      if (order.tax.isNotNullOrEmpty) {
        listAnalystTaxOfProduct.forEach((key, value) {
          totalTaxPrice += ParseUtils.getDouble(value) ?? 0;
        });
      }
    } catch (_) {}
  }

  var timePrinted = now;

  timeCreated = DateTimeUtils.reformatDateTimeString(timeCreated, dateFormat);
  timePrinted = DateTimeUtils.reformatDateTimeString(timePrinted, dateFormat);
  timeStart = DateTimeUtils.reformatDateTimeString(timeStart, dateFormat);
  timeEnd = DateTimeUtils.reformatDateTimeString(timeEnd, dateFormat);

  String lastUpdate = order.lastUpdate ?? order.lastUpdateLocal ?? "";

  if (lastUpdate.isNotEmpty) {
    lastUpdate = DateTimeUtils.reformatDateTimeString(lastUpdate, dateFormat);
  }

  int numPrint = 1;

  if (typePrint == TypePrint.payment || typePrint == TypePrint.debtBill) {
    numPrint = order.numberOfBillsPrinted;
  } else if (typePrint == TypePrint.provisional) {
    numPrint = order.numberOfTicketsPrinted;
  }

  if (numPrint > 1) {
    shortInfo = "(${S.current.copy1} $timePrinted)";
  }

  final logo = await CommonFunc.getShopLogo();

  final paidTotal = order.multipayMethod.isNotNullOrEmpty ? order.getPaymentMethods().sumDouble((e) => e.pay) : 0;

  final currentPoint = ParseUtils.getDouble(exchangeInfo['currentPoint']) ?? 0;
  final remainingPoint = ParseUtils.getDouble(exchangeInfo['pointAfter']) ?? 0;

  final PrintData dataPrint = PrintData(
    orderLocalId: order.localId,
    typePrint: typePrint,
    shortInfo: shortInfo,
    indexOfBill: 1,
    timeCreated: timeCreated,
    timePrinted: timePrinted,
    accountName: accountName,
    shopName: shopBranch?.shopName,
    shopAddress: shopBranch?.address,
    shopPhone: shopBranch?.shopPhone,
    staffName: staffNameCancel ?? staffName,
    staffNamePrint: accountName,
    cashierName: order.cashierName,
    customerName: customerName,
    customerPhone: customerPhone,
    customerAddress: customerAddress,
    placeName: typePrint == TypePrint.moveTable ? tableTo : tableName,
    workShiftName: order.shiftName ?? '',
    orderCode: billCode,
    noteBill: noteBill,
    totalPrice: printOption.totalPriceIncludesDiscountSurcharge
        ? order.total - totalTaxPrice
        : order.total - totalTaxPrice + totalDiscountOfItems - totalSurchargeOfItems,
    totalPay: order.grandTotal,
    totalPaid: paidTotal + refundMoney,
    changeMoney: refundMoney,
    items: items,
    taxPrice: order.taxPrice,
    taxPercent: order.taxPercent,
    deposits: depositsPrice,
    multiPayMethod: paymentMethods,
    timeStartService: startTime,
    discountPrice: order.discountPrice,
    depositData: listDeposits,
    typeService: typeService,
    discountReason: order.discountReason ?? '',
    surchargeReason: order.surchargeReason ?? '',
    partnerDiscountPercent: order.partnerDiscountCreateDebtFlg == 0 ? order.partnerDiscountPercent : 0,
    partnerDiscountPrice: order.partnerDiscountCreateDebtFlg == 0 ? order.partnerDiscount : 0,
    totalTaxPriceItem: totalTaxPriceItem,
    tableTo: typePrint == TypePrint.moveTable ? tableName : tableTo,
    orderMergeCode: typePrint == TypePrint.moveTable ? orderMergeCode : order.code,
    numberOfCustomer: order.personNum,
    totalDiscountOfItems: totalDiscountOfItems,
    totalSurchargeOfItems: totalSurchargeOfItems,
    totalOfItems: totalOfItems,
    surchargePrice: order.surchargePrice,
    shipFee: order.shippingPrice,
    totalPriceOfCancel: totalPriceOfCancel,
    cancelReason: cancelReason,
    discounts: discounts,
    surcharges: surcharges,
    timeIn: timeStart,
    timeOut: timeEnd,
    qrData: qrData,
    customerScanCardBalance: order.customerScanCardBalance,
    customerScanCardBalanceBeforePay: order.customerScanCardBalanceBeforePay,
    customerScanCardPay: order.customerScanCardPayAmount,
    searchKey: order.searchKey,
    urlLookup: order.urlLookup,
    taxAgencyCode: order.codeOfTax,
    invoiceNo: order.invoiceNo,
    sellerTaxCode: order.sellerTaxCode,
    timesPrinted: order.timesPrinted,
    listAnalystTaxOfProduct: listAnalystTaxOfProduct,
    taxSetting: order.tax,
    orderHaveTable: order.tableId != null && order.tableId != 0,
    orderType: order.type,
    remainCustomerPoint: remainCustomerPoint,
    lastUpdate: lastUpdate,
    logo: logo,
    currentPoint: currentPoint,
    remainingPoint: remainingPoint,
  );

  if (order.paidType == 2) {
    dataPrint.debt = order.grandTotal - paidTotal;
  }

  return dataPrint;
}

String getPrintTimeWithFormat() {
  final shopBranch = GetIt.I<ShopBranchUseCase>().getShopBranchByShopId(CommonFunc.getShopId() ?? 0);
  var dateFormat = 'dd-MM HH:mm';
  if (shopBranch != null) {
    dateFormat = '${shopBranch.showDateFormat!} HH:mm';
  }
  return DateTime.now().toStringWithFormat(dateFormat);
}

List<PrinterEnum> _getPrintPlaces(OrderItem item) {
  final printPlaces = <PrinterEnum>[];
  if (item.printPlaces.isNotNullOrEmpty) {
    final list = item.printPlaces!.split('');
    for (final e in list) {
      final place = PrinterEnum.fromId(int.parse(e));
      printPlaces.add(place);
    }
  }

  return printPlaces;
}

(List<ToppingData>, double) _getToppingData(OrderItem item) {
  final toppingData = <ToppingData>[];
  var totalToppingPrice = 0.0;
  for (final option in item.options) {
    final total = option.quantity * option.price * item.number;
    totalToppingPrice += total;
    toppingData.add(
      ToppingData(
        itemName: option.name,
        price: option.price,
        totalPrice: total,
        quantity: option.quantity,
        code: option.code ?? '',
        isProduct: true,
      ),
    );
  }

  return (toppingData, totalToppingPrice);
}

bool _canMerge(List<ToppingData> a, List<ToppingData> b, bool enableMerge) {
  return enableMerge && listEquals(a, b);
}

List<ToppingData> _getListToppingDuplicate(
  List<ToppingData> listTopping,
  double number, {
  bool isPrintCancelProduct = false,
}) {
  for (final e in listTopping) {
    e.totalPrice = isPrintCancelProduct ? 0 : e.quantity * e.price * number;
  }
  return listTopping;
}

PrintData _createPrintDataForReturnItems(Order order, String staffName) {
  final items = <ItemData>[];
  for (final item in order.orderItems) {
    if (item.isChild) continue;
    final (toppingData, totalToppingPrice) = _getToppingData(item);
    final itemPrint = ItemData(
      itemName: item.productName,
      itemQuantity: item.number,
      originalQuantity: item.originalNumber,
      printPlaces: _getPrintPlaces(item),
      toppingData: toppingData,
    );
    items.add(itemPrint);
  }

  final printData = PrintData(
    typePrint: TypePrint.returnItem,
    orderCode: order.code,
    staffName: staffName,
    timePrinted: DateTime.now().toStringWithFormat('dd-MM HH:mm'),
    typeService: 1,
    placeName: [order.areaName, order.tableName].nonNullOrEmpties.join(' - '),
    items: items,
  );

  return printData;
}
