import 'package:common/common.dart';
import 'package:data/data.dart';
import 'package:domain/usecase/setting/setting_use_case.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:resources/resources.dart';
import 'package:sales_core/sales_core.dart';

import '../../bloc/order/ordering_order_bloc.dart';
import '../../pages/print_checking_bill_page.dart';
import '../../pages/return_order_items_page.dart';
import '../../utils/table_actions.dart';

class OrderingOrderActions extends StatelessWidget {
  final bool atCounter;
  final bool showTakeAway;
  final bool showMoreButton;
  final bool showSaveButton;
  final void Function()? onTapCart;
  final void Function(bool kitchenFlg) onSave;
  final void Function() onPay;
  final void Function()? onSaved;

  const OrderingOrderActions({
    required this.atCounter,
    required this.showTakeAway,
    required this.showMoreButton,
    required this.showSaveButton,
    required this.onSave,
    required this.onPay,
    required this.onSaved,
    this.onTapCart,
    super.key,
  });

  bool get _isTablet => AppDimen.current.isTablet;

  bool get _showVibrationPager => atCounter;

  bool get _showTempPrint => !atCounter;

  bool get _showCartButton => onTapCart != null;

  @override
  Widget build(BuildContext context) {
    final bloc = context.read<OrderingOrderBloc>();

    return BlocBuilder<OrderingOrderBloc, OrderingOrderState>(
      buildWhen: (prev, curr) => curr.rebuildActions,
      builder: (ctx, state) => LayoutBuilder(
        builder: (ctx, constraints) => Container(
          color: CColors.white,
          padding: CEdgeInsets.a8,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                children: [
                  if (_showVibrationPager)
                    _buildIconButton(
                      enabled: state.orderState.get().orderItems.isNotEmpty,
                      icon: const CIcon.svg(MyAssets.svgVibrationPager, width: Dimens.d24),
                      // TODO(nam): translate
                      label: 'Thẻ chờ',
                      onTap: () => _inputWaitingPager(context, bloc),
                    ),
                  if (_showTempPrint)
                    _buildIconButton(
                      icon: const CIcon.svg(MyAssets.svgPrint2, width: Dimens.d24),
                      // TODO(nam): translate
                      label: 'In tạm tính',
                      onTap: () => bloc.add(OnPrintProvisional(triggerCallback: true)),
                    ),
                  if (showTakeAway) ...[
                    CSizedBox.w8(),
                    Builder(
                      builder: (_) {
                        final isTakeAway = state.orderState.get().isTakeAway;
                        return _buildIconButton(
                          icon: const CIcon(Icons.shopping_bag_outlined, size: Dimens.d24),
                          label: S.current.takeAway,
                          onTap: () => bloc.add(OnToggleTakeAway(!isTakeAway)),
                          isActiveColor: isTakeAway,
                        );
                      },
                    ),
                  ],
                  CSizedBox.w8(),
                  Expanded(
                    child: Align(
                      alignment: Alignment.centerRight,
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // Quantity
                          Row(
                            mainAxisSize: MainAxisSize.min,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              CText.body(
                                S.of(context).total,
                                color: CColors.steel,
                                fontWeight: FontWeight.bold,
                              ),
                              CSizedBox.w10(),
                              Builder(
                                builder: (context) {
                                  final size = _isTablet ? Dimens.d24 : Dimens.d22;
                                  return Container(
                                    height: size,
                                    constraints: BoxConstraints(minWidth: size),
                                    padding: CEdgeInsets.a4.byHorizontal(),
                                    alignment: Alignment.center,
                                    decoration: BoxDecoration(
                                      color: CColors.steel,
                                      borderRadius: BorderRadius.circular(size / 2),
                                    ),
                                    child: CText.callout(
                                      NumberFormatUtils.format(state.getTotalQuantity(), isCurrency: false),
                                      color: CColors.white,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  );
                                },
                              ),
                            ],
                          ),
                          CSizedBox.h1(),
                          // Total
                          FittedBox(
                            fit: BoxFit.scaleDown,
                            child: CText.headline(
                              NumberFormatUtils.format(state.getTotalAmount()),
                              color: CColors.boogieBlast,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  CSizedBox.w8(),
                ],
              ),
              CSizedBox.h5(),
              Padding(
                padding: CEdgeInsets.a4.byLeft(),
                child: Row(
                  children: [
                    if (showMoreButton) ...[
                      IconButton(
                        icon: const CIcon(
                          Icons.keyboard_arrow_up_rounded,
                          grade: -25,
                          size: Dimens.d28,
                        ),
                        style: IconButton.styleFrom(
                          foregroundColor: CColors.white,
                          backgroundColor: AppColors.current.secondaryColor,
                          shape: const RoundedRectangleBorder(borderRadius: CBorderRadius.c4),
                          fixedSize: const Size(Dimens.d48, Dimens.d48),
                        ),
                        onPressed: () {
                          showCupertinoModalPopup(
                            context: context,
                            builder: (ctx) => BlocProvider.value(
                              value: context.read<OrderingOrderBloc>(),
                              child: _MenuMore(
                                context,
                                constraints.maxWidth,
                                onSave: () => onSave(false),
                                onSaved: onSaved,
                              ),
                            ),
                          );
                        },
                      ),
                      CSizedBox.w10(),
                    ],
                    if (_showCartButton)
                      SCCartButton(
                        quantity: state.getUnsavedQuantity(),
                        onTap: onTapCart,
                      ),
                    if (showSaveButton) ...[
                      Expanded(
                        child: CButton.custom(
                          enabled: state.orderState.get().orderItems.isNotEmpty,
                          // TODO(nam): translate
                          label: bloc.state.enablePrintKitchen ? 'Báo bếp' : 'Lưu tạm',
                          backgroundColor: CColors.burningOrange,
                          borderRadius: CBorderRadius.c4.left(),
                          onTap: () => onSave(bloc.state.enablePrintKitchen),
                        ),
                      ),
                    ],
                    Expanded(
                      child: CButton.pay(
                        enabled: state.orderState.get().orderItems.isNotEmpty,
                        borderRadius: _showCartButton || showSaveButton ? CBorderRadius.c4.right() : CBorderRadius.c4,
                        onTap: onPay,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildIconButton({
    required CIcon icon,
    required String label,
    required void Function() onTap,
    bool enabled = true,
    bool isActiveColor = false,
  }) {
    final foregroundColor = isActiveColor ? CColors.burningOrange : CColors.steel;
    return Material(
      color: CColors.white,
      child: InkWell(
        splashColor: CColors.burningOrange.withOpacity(0.2),
        splashFactory: InkSplash.splashFactory,
        borderRadius: CBorderRadius.c3,
        onTap: enabled ? onTap : null,
        child: Padding(
          padding: CEdgeInsets.a4,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              icon.copyWith(color: foregroundColor),
              CText.callout(label, color: foregroundColor),
            ],
          ),
        ),
      ),
    );
  }

  void _inputWaitingPager(BuildContext context, OrderingOrderBloc bloc) {
    final shopOptions = GetIt.I<SettingUseCase>().getShopOptionsLocal();
    final typeSelect = SelectTableBy.fromId(shopOptions.typeSelectTableName);
    if (typeSelect == SelectTableBy.selectRoom) {
      TableActions.performSelectTable(
        context,
        onSelected: (table, printFlg) {
          Navigator.of(context).pop();
          bloc.add(OnSetWaitingPager(table.name ?? '', tableId: table.tableId, onSuccess: onPay));
        },
      );
    } else {
      showDialog(
        context: context,
        builder: (_) => QuickEntryTableDialog(
          // TODO(nam): translate
          title: 'Thẻ chờ',
          actionsBuilder: (getName) => [
            CButton.save(
              onTap: () {
                final name = getName();
                if (name == null) return;
                Navigator.of(context).pop();
                bloc.add(OnSetWaitingPager(name, onSuccess: () => onSave(false)));
              },
            ),
            CButton.pay(
              onTap: () {
                final name = getName();
                if (name == null) return;
                Navigator.of(context).pop();
                bloc.add(OnSetWaitingPager(name, onSuccess: onPay));
              },
            ),
          ],
        ),
      );
    }
  }
}

class _MenuMore extends StatelessWidget {
  final BuildContext parentContext;
  final double width;
  final void Function() onSave;
  final void Function()? onSaved;

  const _MenuMore(this.parentContext, this.width, {required this.onSave, required this.onSaved});

  bool get _isTablet => AppDimen.current.isTablet;

  @override
  Widget build(BuildContext context) {
    final bloc = context.read<OrderingOrderBloc>();

    return Align(
      alignment: _isTablet ? Alignment.bottomRight : Alignment.bottomCenter,
      child: SizedBox(
        width: _isTablet ? width : null,
        child: CupertinoActionSheet(
          // TODO(nam): translate
          actions: [
            _buildItem(
              title: 'Hủy đơn hàng'.untranslated,
              color: CColors.destructive,
              onTap: () {
                Navigator.of(context).pop();
                ConfirmCancelOrderDialog.show(
                  parentContext,
                  onYes: (reason, printFlg) {
                    bloc.add(OnCancelOrder(reason, printFlg));
                  },
                );
              },
            ),
            _buildItem(
              // TODO(nam): translate
              title: 'Chuyển bàn'.untranslated,
              onTap: () {
                if (_checkNeedToSaveOrder(bloc)) return;

                Navigator.of(context).pop();
                TableActions.performMoveAction(
                  parentContext,
                  order: bloc.state.orderState.get().clone(),
                  onSelected: (table, printFlg) {
                    Navigator.of(parentContext).pop();
                    bloc.add(OnMoveToTable(table, onSave, isPrint: printFlg));
                  },
                );
              },
            ),
            _buildItem(
              title: 'Gộp bàn'.untranslated,
              onTap: () {
                if (_checkNeedToSaveOrder(bloc)) return;

                Navigator.of(context).pop();
                TableActions.performMergeAction(
                  parentContext,
                  order: bloc.state.orderState.get().clone(),
                  onSelected: (order, printFlg) {
                    Navigator.of(parentContext).pop();
                    bloc.add(
                      OnMergeToOrder(
                        order,
                        printFlg,
                        onSuccess: () => Navigator.of(parentContext).pop(),
                      ),
                    );
                  },
                );
              },
            ),
            _buildItem(
              title: 'Tách đơn'.untranslated,
              onTap: () {
                if (_checkNeedToSaveOrder(bloc)) return;

                Navigator.of(context).pop();
                TableActions.performSplitAction(
                  parentContext,
                  order: bloc.state.orderState.get().clone(),
                );
              },
            ),
            _buildItem(
              title: 'In tạm tính'.untranslated,
              onTap: () => bloc.add(OnPrintProvisional(triggerCallback: false)),
            ),
            _buildItem(
              title: 'In kiểm món'.untranslated,
              onTap: () {
                if (_checkNeedToSaveOrder(bloc)) return;
                Navigator.of(context).pop();
                final args = PrintCheckingBillPageArgs(
                  order: bloc.state.orderState.get(),
                  onSuccess: onSaved,
                );
                ViewUtils.openPage(
                  context,
                  routeName: PrintCheckingBillPage.routeName,
                  arguments: args,
                  pageBuilder: () => PrintCheckingBillPage(args: args),
                );
              },
            ),
            _buildItem(
              title: 'Trả món'.untranslated,
              onTap: () {
                if (_checkNeedToSaveOrder(bloc)) return;
                Navigator.of(context).pop();

                final args = ReturnOrderItemsPageArgs(
                  order: bloc.state.orderState.get(),
                  onSuccess: onSaved,
                );

                ViewUtils.openPage(
                  context,
                  routeName: ReturnOrderItemsPage.routeName,
                  arguments: args,
                  pageBuilder: () => ReturnOrderItemsPage(args: args),
                );
              },
            ),
            _buildItem(
              title: 'In bếp'.untranslated,
              onTap: () {
                if (_checkNeedToSaveOrder(bloc)) return;
                Navigator.of(context).pop();
                final args = PrintKitchenItemsPageArgs(
                  order: bloc.state.orderState.get(),
                  onSuccess: onSaved,
                );
                ViewUtils.openPage(
                  context,
                  routeName: PrintKitchenItemsPage.routeName,
                  arguments: args,
                  pageBuilder: () => PrintKitchenItemsPage(args: args),
                );
              },
            ),
            _buildItem(
              title: 'Chỉ lưu'.untranslated,
              onTap: () {
                Navigator.of(context).pop();
                onSave();
              },
            ),
          ],
          cancelButton: CupertinoActionSheetAction(
            onPressed: () => Navigator.of(context).pop(),
            child: CText.body(
              S.current.close,
              color: CColors.destructive,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildItem({
    required String title,
    required void Function() onTap,
    Color? color,
  }) {
    return CupertinoActionSheetAction(
      onPressed: onTap,
      child: CText.body(
        title,
        color: color,
        textAlign: TextAlign.center,
        maxLines: 2,
      ),
    );
  }

  bool _checkNeedToSaveOrder(OrderingOrderBloc bloc) {
    if (bloc.checkOrderHasChanges()) {
      // TODO(nam): translate
      ViewUtils.showToastError('Vui lòng lưu đơn hàng trước khi thực hiện thao tác!'.untranslated);
      return true;
    }

    return false;
  }
}
