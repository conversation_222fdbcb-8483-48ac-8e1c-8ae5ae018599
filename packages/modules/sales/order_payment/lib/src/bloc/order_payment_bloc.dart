// ignore_for_file: non_constant_identifier_names

import 'dart:convert';

import 'package:common/common.dart';
import 'package:data/data.dart';
import 'package:domain/dto/customer_prepaid_card_dto.dart';
import 'package:domain/dto/discharge_dto.dart';
import 'package:domain/dto/earn_points_dto.dart';
import 'package:domain/dto/member_card_dto.dart';
import 'package:domain/dto/payment_method_dto.dart';
import 'package:domain/dto/print_option_dto.dart';
import 'package:domain/dto/qr_data_dto.dart';
import 'package:domain/usecase/asset/asset_use_case.dart';
import 'package:domain/usecase/customer/customer_use_case.dart';
import 'package:domain/usecase/order/order_use_case.dart';
import 'package:domain/usecase/setting/setting_use_case.dart';
import 'package:domain/usecase/staff/staff_use_case.dart';
import 'package:domain/usecase/staff_collaborator_partner/staff_collaborator_partner_use_case.dart';
import 'package:e_invoice_info/e_invoice_info.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:payment_core/payment_core.dart';
import 'package:printing/printing.dart';
import 'package:resources/resources.dart';
import 'package:sales_core/sales_core.dart';
import 'package:services/services.dart';
import 'package:socket_manager/socket_manager.dart';

import '../enum/enum.dart';
import '../pages/order_payment_page.dart';
import '../states/commision_expense_state.dart';
import '../states/e_invoice_state.dart';
import '../utils/order_payment_utils.dart';
import '../view_models/asset_or_group.dart';

part 'order_payment_event.dart';
part 'order_payment_state.dart';

class OrderPaymentBloc extends Bloc<OrderPaymentEvent, OrderPaymentState> {
  final SettingUseCase _settingUseCase;
  final CustomerUseCase _customerUseCase;
  final AssetUseCase _assetUseCase;
  final OrderUseCase _orderUseCase;
  final ProductStockService _productStockService;
  final StaffUseCase _staffUseCase;
  final StaffCollaboratorPartnerUseCase _staffCollaboratorPartnerUseCase;
  final MessageHandler _messageHandler;

  OrderPaymentBloc(
    this._settingUseCase,
    this._customerUseCase,
    this._assetUseCase,
    this._orderUseCase,
    this._productStockService,
    this._staffUseCase,
    this._staffCollaboratorPartnerUseCase,
    this._messageHandler,
    Order order,
    this._onlineFlg,
  ) : super(OrderPaymentState(orderState: ObjectState(order, extraKeys: (o) => [o.localId]))) {
    on<OnInitOrderPayment>(_onInitOrderPayment);
    on<OnChangeOrderNote>(_onChangeOrderNote);
    on<OnChangeCustomer>(_onChangeCustomer);
    on<OnApplyPromotions>(_onApplyPromotions);
    on<OnDeleteOrderPromotion>(_onDeleteOrderPromotion);
    on<OnChangeOrderDiscount>(_onChangeOrderDiscount);
    on<OnChangeOrderSurcharge>(_onChangeOrderSurcharge);
    on<OnChangeOrderTax>(_onChangeOrderTax);
    on<OnToggleOrderDiscountPercent>(_onToggleOrderDiscountPercent);
    on<OnToggleOrderSurchargePercent>(_onToggleOrderSurchargePercent);
    on<OnSelectNewAsset>(_onSelectNewAsset, transformer: BlocConcurrency.droppable());
    on<OnRemoveCurrentAsset>(_onRemoveCurrentAsset);
    on<OnPayOrder>(_onPayOrder);
    on<OnChangeEInvoiceState>(_onChangeEInvoiceState);
    on<OnUpdateOrderInfoAfterDistributon>(_onUpdateOrderInfoAfterDistributon);
    on<OnChangeCommissionType>(_onChangeCommissionType);
    on<OnChangeCommissionAmount>(_onChangeCommissionAmount);
    on<OnChangeStaffOrPartner>(_onChangeStaffOrPartner);
    on<OnChangePrintFlagWithType>(_onChangePrintFlagWithType);
    on<OnRemoveAssetByType>(_onRemoveAssetByType);
    on<OnGetEarnsPoints>(_onGetEarnsPoints);
    on<OnApplyMemberCardDiscount>(_onApplyMemberCardDiscount);
  }

  final bool _onlineFlg;
  bool _partnerInventoryDeductionEnabled = false;

  final discountReasonTec = TextEditingController();
  final surchargeReasonTec = TextEditingController();
  final commissionReasonTec = TextEditingController();

  final methodScrollController = ScrollController();
  final amountTec = TextEditingController();
  final changeAmount = ValueNotifier<double>(0);

  double get currentAmount => NumberFormatUtils.parseDouble(amountTec.text) ?? 0;

  List<DischargeDto> _presetDiscounts = [];
  List<DischargeDto> _presetSurcharges = [];

  VoidCallback? successCallback;

  Future<void> _onInitOrderPayment(OnInitOrderPayment event, Emitter<OrderPaymentState> emit) async {
    final order = event.args.order;
    final shopOptions = _settingUseCase.getShopOptionsLocal();

    _presetDiscounts = shopOptions.getDiscounts();
    _presetSurcharges = shopOptions.getSurcharges();
    _partnerInventoryDeductionEnabled = shopOptions.partnerInventoryDeductionEnabled == 1;

    Customer? customer = event.args.customer;
    final customerId = order.customerId;
    if (customerId.isNotNullOrZero) {
      customer ??= await _customerUseCase.getCustomerById(customerId!);
    }

    final appliedPromotions = PromotionHelper.getPromotionsAppliedFromOrder(order);

    discountReasonTec.text = order.discountReason ?? '';
    surchargeReasonTec.text = order.surchargeReason ?? '';

    final allSaleChannels = _settingUseCase.loadAllSaleChannels();
    final saleChannel = allSaleChannels.firstWhereOrNull((e) => e.channelId == order.channelId);
    Asset? defaultAsset;

    bool isDefaultAsset(Asset asset) {
      final assetId = saleChannel?.assetId;
      if (assetId != null) return asset.assetId == assetId;
      return asset.defaultFlg == 1;
    }

    final List<AssetOrGroup> assetModels = [];

    if (shopOptions.displayAssetGroup == 1) {
      final assetGroups = _assetUseCase.getAllAssetsGroupDB();
      for (final group in assetGroups) {
        if (group.assetGroupName.isNullOrEmpty) {
          assetModels.addAll(AssetOrGroup.fromAssets(group.assets));
          defaultAsset ??= group.assets.firstWhereOrNull(isDefaultAsset);
        } else {
          assetModels.add(AssetOrGroup.group(group));
        }
      }
    } else {
      final assets = _assetUseCase.getAllAssetsDB();
      assetModels.addAll(AssetOrGroup.fromAssets(assets));
      defaultAsset ??= assets.firstWhereOrNull(isDefaultAsset);
    }

    assetModels.add(AssetOrGroup.asset(Asset.debt()));

    final oldMethods = order.getPaymentMethods();

    amountTec.addListener(_updateChangeAmount);

    final autoExportVAT = shopOptions.autoExportVAT == 1 && EInvoiceManager.i.setting.canExport;
    final eInvoieInfo = _getEInvoiceInfo(customer, autoExportVAT, isInit: true);

    final displayPartnerPayment = shopOptions.displayPartnerPayment;
    final commisionExpenseState = _getCommisionExpenseInfo(shopOptions);

    final printKitchen = order.tableId.isNotNullOrZero
        ? shopOptions.printOptionDto.isPrintKitchenWhenPaymentTable
        : shopOptions.printOptionDto.isPrintKitchenWhenPayment;

    emit(
      OrderPaymentState(
        isInited: true,
        orderState: eInvoieInfo != null
            ? state.orderState.update(
                (order) => order
                  ..eInvoice = jsonEncode(eInvoieInfo)
                  ..vatFlg = 1,
              )
            : state.orderState,
        rebuildAppBar: true,
        rebuildPromotions: true,
        rebuildAdjustment: true,
        rebuildMethods: true,
        customer: customer,
        appliedPromotions: appliedPromotions,
        assetModels: assetModels,
        oldMethods: oldMethods,
        eInvoiceState: EInvoiceState(
          isSelected: autoExportVAT,
          invoiceType: customer?.typeCustomer ?? 0,
        ),
        typeTaxCalculation: shopOptions.typeTaxCalculation,
        displayPartnerPayment: displayPartnerPayment,
        printOption: shopOptions.printOptionDto,
        commisionExpenseState: commisionExpenseState,
        quickChangePrintPayment: shopOptions.quickChangeStatePrintPayment,
        quickChangePrintKitchen: shopOptions.showQuickChangeStatePrintKitchen == 1,
        quickChangePrintStamp: shopOptions.printOptionDto.onOffPrintStampWhenSelling,
        printKitchen: printKitchen,
        printStamp: printKitchen,
      ),
    );

    event.onInited?.call(defaultAsset);
  }

  EInvoiceInfo? _getEInvoiceInfo(
    Customer? customer,
    bool autoExportVAT, {
    bool isInit = false,
  }) {
    if (autoExportVAT) {
      final eInvoice = EInvoiceInfo(
        batchId: CommonFunc.uuidv4,
        sendMailToCustomer: state.eInvoiceState.sendMailToCustomer,
        publishType: state.eInvoiceState.publishType,
        customer: CustomerEInvoiceInfo(
          customerId: customer?.id,
          customerName: customer?.name,
          customerAddress: customer?.typeCustomer == 0 ? customer?.address : customer?.companyAddress,
          customerTaxCode: customer?.taxCode,
          customerPhoneNumber: customer?.tel,
          customerEmail: customer?.email,
          customerCompanyName: customer?.companyName,
          customerType: isInit ? customer?.typeCustomer : state.eInvoiceState.invoiceType,
        ),
      );

      return eInvoice;
    }

    return null;
  }

  void _updateChangeAmount() {
    final totalAmount = state.getTotalAmount();
    final legitAmount = state.legitAmount;

    double change = legitAmount + currentAmount - totalAmount;
    if (change < 0) change = 0;
    changeAmount.value = change;

    if (!methodScrollController.hasClients) return;
    Future.delayed(50.milliseconds, () {
      methodScrollController.animateTo(
        methodScrollController.position.maxScrollExtent,
        duration: 150.milliseconds,
        curve: Curves.easeInOut,
      );
    });
  }

  void _updateRemainAmount(OrderPaymentState? eState) {
    final remainAmount = (eState ?? state).remainAmount;
    amountTec.text = NumberFormatUtils.format(remainAmount);
  }

  Future<void> _onChangeOrderNote(OnChangeOrderNote event, Emitter<OrderPaymentState> emit) async {
    final newOrderState = state.orderState.update(
      (order) => order..note = event.note,
    );

    emit(state.copyWith(orderState: newOrderState));
  }

  Future<void> _onChangeCustomer(OnChangeCustomer event, Emitter<OrderPaymentState> emit) async {
    EInvoiceInfo? eInvoice;
    if (state.eInvoiceState.isSelected) {
      eInvoice = _getEInvoiceInfo(event.customer, true);
    }

    final newOrderState = state.orderState.update(
      (order) => order
        ..customerId = event.customer?.customerId
        ..customerName = event.customer?.name
        ..customeEmail = event.customer?.email
        ..customerPhoneNumber = event.customer?.tel
        ..customerAddress = event.customer?.address
        ..eInvoice = eInvoice != null ? jsonEncode(eInvoice) : null
        ..vatFlg = eInvoice != null ? 1 : 0,
    );

    if (event.customer != null) {
      add(OnGetEarnsPoints());
    } else {
      final order = state.orderState.get();
      final discounts = order.getExtraDiscounts();
      final discount = discounts.firstWhereOrNull((e) => e.type == PromotionTypeEnum.rankTreatmentCard.id);
      if (discount != null) {
        add(OnDeleteOrderPromotion(discount));
      }
    }

    emit(
      state.copyWith(
        rebuildAppBar: true,
        orderState: newOrderState,
        customer: Wrapped(event.customer),
        customerLoyalty: Wrapped(event.customer != null ? state.customerLoyalty : null),
      ),
    );
  }

  Future<void> _onApplyPromotions(OnApplyPromotions event, Emitter<OrderPaymentState> emit) async {
    final result = PromotionHelper.applyManualPromotion(
      promotionSelected: [
        ...state.appliedPromotions.where((e) => !e.isOrderPromotion),
        ...event.promotions,
      ],
      input: state.orderState.get(),
      groupCustomerId: state.customer?.groupid,
      applied: [...state.appliedPromotions],
    );

    final newOrderState = state.orderState.update(
      (order) {
        order = result.order;
        return order;
      },
    );

    discountReasonTec.text = newOrderState.get().discountReason ?? '';
    surchargeReasonTec.text = newOrderState.get().surchargeReason ?? '';

    final newState = state.copyWith(
      rebuildPromotions: true,
      rebuildAdjustment: true,
      orderState: newOrderState,
      appliedPromotions: result.applied,
    );

    emit(newState);

    _updateRemainAmount(newState);
  }

  Future<void> _onDeleteOrderPromotion(OnDeleteOrderPromotion event, Emitter<OrderPaymentState> emit) async {
    ObjectState<Order>? newOrderState;
    List<Promotion>? appliedPromotions;

    final promotionId = event.item.promotionId;
    if (promotionId == null) return;

    newOrderState = state.orderState.update(
      (order) {
        final result = PromotionHelper.removePromotionByType(
          order,
          event.item.type,
          promotionId,
          appliedInput: state.appliedPromotions,
        );
        appliedPromotions = result.applied;
        return result.order;
      },
    );

    discountReasonTec.text = newOrderState.get().discountReason ?? '';
    surchargeReasonTec.text = newOrderState.get().surchargeReason ?? '';

    final newState = state.copyWith(
      rebuildPromotions: true,
      rebuildAdjustment: true,
      orderState: newOrderState,
      appliedPromotions: appliedPromotions,
    );

    emit(newState);

    _updateRemainAmount(newState);
  }

  Future<void> _onChangeOrderDiscount(OnChangeOrderDiscount event, Emitter<OrderPaymentState> emit) async {
    final newOrderState = state.orderState.update(
      (order) {
        final config = event.config;
        List<DischargeDto> items;
        if (config != null) {
          items = [
            if (config.value > 0)
              DischargeDto.manual(
                type: PromotionTypeEnum.manual.id,
                isPercent: config.isPercent,
                percent: config.isPercent ? config.value : 0,
                price: config.isPercent ? 0 : config.value,
                reason: config.reason ?? '',
                applyOnOriginalPriceFlg: config.afterPromotionFlg ? 0 : 1,
              ),
          ];
        } else if (event.items != null) {
          items = event.items ?? [];
        } else {
          items = [];
        }
        order.addExtraDiscounts(items, manual: true);
        return OrderUtils.recalcDiscounts(order);
      },
    );

    discountReasonTec.text = newOrderState.get().discountReason ?? '';

    final newState = state.copyWith(
      rebuildAdjustment: true,
      orderState: newOrderState,
    );

    emit(newState);

    _updateRemainAmount(newState);
  }

  Future<void> _onChangeOrderSurcharge(OnChangeOrderSurcharge event, Emitter<OrderPaymentState> emit) async {
    final newOrderState = state.orderState.update(
      (order) {
        final config = event.config;
        List<DischargeDto> items;
        if (config != null) {
          items = [
            if (config.value > 0)
              DischargeDto.manual(
                type: PromotionTypeEnum.manual.id,
                isPercent: config.isPercent,
                percent: config.isPercent ? config.value : 0,
                price: config.isPercent ? 0 : config.value,
                reason: config.reason ?? '',
              ),
          ];
        } else if (event.items != null) {
          items = event.items ?? [];
        } else {
          items = [];
        }
        order.addExtraSurcharges(items, manual: true);
        return OrderUtils.recalcSurcharges(order);
      },
    );

    surchargeReasonTec.text = newOrderState.get().surchargeReason ?? '';

    final newState = state.copyWith(
      rebuildAdjustment: true,
      orderState: newOrderState,
    );

    emit(newState);

    _updateRemainAmount(newState);
  }

  Future<void> _onChangeOrderTax(OnChangeOrderTax event, Emitter<OrderPaymentState> emit) async {
    final newOrderState = state.orderState.update(
      (order) {
        order.taxPercent = event.percent;
        order = recalculate(order);
        return order;
      },
    );

    final newState = state.copyWith(
      rebuildAdjustment: true,
      orderState: newOrderState,
    );

    emit(newState);

    _updateRemainAmount(newState);
  }

  Future<void> _onToggleOrderDiscountPercent(
    OnToggleOrderDiscountPercent event,
    Emitter<OrderPaymentState> emit,
  ) async {
    final newOrderState = state.orderState.update(
      (order) {
        final extraDiscounts = order.getExtraDiscounts();

        for (int i = 0; i < extraDiscounts.length; i++) {
          final discount = extraDiscounts[i];
          if (discount.type != PromotionTypeEnum.manual.id) continue;
          extraDiscounts[i] = discount.copyWith(isPercent: event.isPercent);
        }

        return order..setExtraDiscounts(extraDiscounts);
      },
    );

    emit(
      state.copyWith(
        rebuildAdjustment: true,
        orderState: newOrderState,
      ),
    );
  }

  Future<void> _onToggleOrderSurchargePercent(
    OnToggleOrderSurchargePercent event,
    Emitter<OrderPaymentState> emit,
  ) async {
    final newOrderState = state.orderState.update(
      (order) {
        final extraSurcharges = order.getExtraSurcharges();

        for (int i = 0; i < extraSurcharges.length; i++) {
          final surcharge = extraSurcharges[i];
          if (surcharge.type != PromotionTypeEnum.manual.id) continue;
          extraSurcharges[i] = surcharge.copyWith(isPercent: event.isPercent);
        }

        return order..setExtraSurcharges(extraSurcharges);
      },
    );

    emit(
      state.copyWith(
        rebuildAdjustment: true,
        orderState: newOrderState,
      ),
    );
  }

  PaymentMethodDto? _getCurrentPaymentMethod({Asset? newAsset}) {
    final asset = newAsset ?? state.currentAsset;
    if (asset == null) return null;

    final totalAmount = state.getTotalAmount();
    final legitAmount = state.legitAmount;

    double effectiveAmount = asset.pay ?? currentAmount;

    if ((legitAmount + currentAmount >= totalAmount) && asset.pay == null) {
      effectiveAmount = totalAmount - legitAmount;
    }

    final newMethod = PaymentMethodDto(
      transactionLocalId: CommonFunc.randomId(),
      masterShopId: asset.shopId,
      assetId: asset.assetId,
      assetName: asset.name,
      assetType: asset.type ?? 0,
      noRevenueFlg: asset.noRevenueFlg ?? 0,
      ewalletType: asset.eWalletType ?? 0,
      ewalletInfo: asset.eWalletInfo ?? '',
      pay: effectiveAmount,
      ewalletTransactionLocalId: asset.ewalletTransactionLocalId ?? '',
      prepaidCardChargedId: asset.prepaidCardChargedId ?? '',
      prepaidCardDebtAmount: asset.prepaidCardDebtAmount,
      customerTreatmentCardId: asset.cardId ?? 0,
      customerName: asset.customerName ?? '',
      customerId: asset.customerId ?? 0,
      customerUsedPoint: asset.customerUsedPoint ?? 0,
      voucherDetail: asset.voucherDetail,
    );

    return newMethod;
  }

  Future<void> _onSelectNewAsset(OnSelectNewAsset event, Emitter<OrderPaymentState> emit) async {
    // Nếu chọn lại quỹ tiền đã thêm trước đó, thì không làm gì
    if (state.addedMethods.any((e) => e.assetId == event.asset.assetId)) return;

    // Bỏ chọn quỹ tiền hiện tại
    if (state.currentAsset?.assetId == event.asset.assetId) {
      add(OnRemoveCurrentAsset());
      return;
    }

    autoApplyDisSur(event.asset.assetId);

    List<PaymentMethodDto>? newAddedMethods;
    final newCurrentAsset = event.asset; // qũy tiền vừa chọn sẽ trở thành quỹ tiền hiện tại
    final currentMethod = _getCurrentPaymentMethod(); // quỹ tiền hiện tại có thể lưu được vào order
    final actualAmount = NumberFormatUtils.parseDouble(amountTec.text) ?? 0;
    final total = state.getTotalAmount();

    double amount = 0;

    // Replace
    if (currentMethod == null || state.legitAmount + actualAmount >= total) {
      amount = total - state.legitAmount;
    }
    // Add
    else {
      newAddedMethods = List<PaymentMethodDto>.from(state.addedMethods);
      newAddedMethods.add(currentMethod);
      amount = total - state.legitAmount - currentMethod.pay;
    }

    final assetType = AssetType.fromAsset(newCurrentAsset);

    if (newCurrentAsset.needPaymentProcess) {
      if (!ShiftManager().canOrder) return;
      if (assetType == AssetType.debt) {
        await _handlePaymentDebt(
          context: event.context,
          amount: amount,
          newCurrentAsset: newCurrentAsset,
          newAddedMethods: newAddedMethods,
          emit: emit,
        );
      } else if (assetType == AssetType.treatment && event.scanCardId != null) {
        await _handleTreatmentCard(
          context: event.context,
          amount: amount,
          scanCardId: event.scanCardId!,
          newCurrentAsset: newCurrentAsset,
          newAddedMethods: newAddedMethods,
          emit: emit,
        );
      } else if (assetType == AssetType.mpos) {
        await _handlePaymentMpos(
          context: event.context,
          amount: amount,
          newCurrentAsset: newCurrentAsset,
          newAddedMethods: newAddedMethods,
          emit: emit,
        );
      } else {
        await _showPaymentProcess(
          context: event.context,
          amount: amount,
          newCurrentAsset: newCurrentAsset,
          newAddedMethods: newAddedMethods,
          emit: emit,
        );
      }
    } else {
      if (assetType == AssetType.voucher) {
        final newAmount = newCurrentAsset.voucherDetail?.voucherPrice ?? amount;
        amount = newAmount > amount ? amount : newAmount;
      } else if (assetType == AssetType.point && event.customerLoyalty != null) {
        final newAmount = event.customerLoyalty!.applicablePrice ?? 0;
        amount = newAmount > amount ? amount : newAmount;
      }

      amountTec.text = NumberFormatUtils.format(amount);

      emit(
        state.copyWith(
          rebuildMethods: true,
          addedMethods: newAddedMethods,
          currentAsset: Wrapped(newCurrentAsset),
          voucherDetail: Wrapped(
            assetType == AssetType.voucher ? newCurrentAsset.voucherDetail : state.voucherDetail,
          ),
          customerLoyalty: Wrapped(event.customerLoyalty ?? state.customerLoyalty),
        ),
      );
    }
  }

  Future<void> _onRemoveCurrentAsset(OnRemoveCurrentAsset event, Emitter<OrderPaymentState> emit) async {
    List<PaymentMethodDto>? newAddedMethods;
    Asset? newCurrentAsset;
    String pay = '';
    final assetType = state.currentAsset?.type;
    if (state.addedMethods.isNotEmpty) {
      final lastMethod = state.addedMethods.last;
      newAddedMethods = List<PaymentMethodDto>.from(state.addedMethods)..removeLast();
      newCurrentAsset = state.findAssetById(lastMethod.assetId);
      pay = NumberFormatUtils.format(lastMethod.pay);
    }

    var customerLoyalty = state.customerLoyalty?.copyWith();

    if (assetType == AssetType.point.id) {
      customerLoyalty = customerLoyalty?.copyWith(
        applicablePoint: 0,
        applicablePrice: 0,
      );
    }

    emit(
      state.copyWith(
        rebuildMethods: true,
        addedMethods: newAddedMethods,
        currentAsset: Wrapped(newCurrentAsset),
        voucherDetail: Wrapped(
          assetType == AssetType.voucher.id ? null : state.voucherDetail,
        ),
        customerLoyalty: Wrapped(customerLoyalty),
      ),
    );

    amountTec.text = pay;
  }

  Order applyDischargeReason(Order order) {
    Order newOrder = order;
    var manualDiscount = order.getUnifiedManualDiscount();
    if (manualDiscount != null && manualDiscount.reason != discountReasonTec.text) {
      manualDiscount = manualDiscount.copyWith(reason: discountReasonTec.text);
      order.addExtraDiscounts([manualDiscount], manual: true);
      newOrder = OrderUtils.recalcDiscounts(order);
    }
    var manualSurcharge = order.getUnifiedManualSurcharge();
    if (manualSurcharge != null && manualSurcharge.reason != surchargeReasonTec.text) {
      manualSurcharge = manualSurcharge.copyWith(reason: surchargeReasonTec.text);
      order.addExtraSurcharges([manualSurcharge], manual: true);
      newOrder = OrderUtils.recalcSurcharges(order);
    }
    return newOrder;
  }

  (Map<int, double>, Map<int, double>) _prepareForStockCommiting() {
    final Map<int, double> mapUnsaved = {};
    final Map<int, double> mapSaved = {};

    final orderItems = state.orderState.get().orderItems;
    for (final item in orderItems) {
      if (item.status == OrderStatusEnum.cancelled.id) continue;
      if (item.status == OrderStatusEnum.merged.id) continue;
      final itemMap = ProductStockUtils.countProductOfOrderItem(item, -1);
      if (item.status == OrderStatusEnum.unsaved.id) {
        mapUnsaved.addAll(itemMap);
      } else {
        mapSaved.addAll(itemMap);
      }
    }
    return (mapUnsaved, mapSaved);
  }

  Future<void> _onPayOrder(OnPayOrder event, Emitter<OrderPaymentState> emit) async {
    if (!state.isInited) return;
    // == Validate =======================================================

    if (!ShiftManager().canOrder) return;

    final newAmount = state.addedAmount + currentAmount;

    double wholeAmount = state.oldAmount + newAmount;
    final totalAmount = state.getTotalAmount();

    if (newAmount == 0 && state.getTotalAmount() > 0) {
      ViewUtils.showToastError(S.current.pleaseInputPaymentAmount);
      return;
    }

    // Xử lý hàng thập phân ngầm:
    // Nếu trên giao diện số tiền được thanh toán = số tiền cần thanh toán, nhưng thực tế số lẻ khác nhau
    bool lastMethodIsDiffValue = false;
    if (NumberFormatUtils.format(wholeAmount) == NumberFormatUtils.format(totalAmount) && wholeAmount != totalAmount) {
      lastMethodIsDiffValue = true;
      wholeAmount = totalAmount;
    }
    bool allowPartialPayment = false;
    if (wholeAmount < totalAmount && !event.isDebit) {
      allowPartialPayment = await OrderPaymentUtils.askPartialPayment(
        event.context,
        total: totalAmount,
        partial: wholeAmount,
      );
      if (!allowPartialPayment) return;
    }

    // ====================================================================

    Order order = state.orderState.get().clone();

    final (mapCountUnsaved, mapCountSaved) = _prepareForStockCommiting();
    void commitStock() {
      _productStockService.batchUpdate(mapCountUnsaved, paymentFlg: true, savedFlg: false);
      _productStockService.batchUpdate(mapCountSaved, paymentFlg: true);
    }

    if (order.status == OrderStatusEnum.unsaved.id) {
      if (_partnerInventoryDeductionEnabled) {
        final success = await WarehouseAssignerHelper.assignWarehousesForOrder(order);
        if (!success) return;
      }
    }

    // -- Bổ sung lí do giảm giá, phụ thu --------------------------------

    order = applyDischargeReason(order);

    // ---------------------------------------------------------------------

    final shopOptions = _settingUseCase.getShopOptionsLocal();
    final isTaxByOrder = shopOptions.typeTaxCalculation == 1;
    final notTaxable = shopOptions.notTaxable;

    final nowStr = SalesUtils.getNowString();

    List<PaymentMethodDto> newMethods = [];

    // -- Bổ sung thông tin cho các phương thức thanh toán, và thêm vào đơn -

    newMethods = [...state.addedMethods, if (!event.isDebit) _getCurrentPaymentMethod()].nonNulls.toList();

    if (lastMethodIsDiffValue) {
      final exactValue = totalAmount - state.oldAmount - newMethods.sumDouble((e) => e.pay) + newMethods.last.pay;
      newMethods[newMethods.length - 1] = newMethods.last.copyWith(pay: exactValue);
    }

    for (int i = 0; i < newMethods.length; i++) {
      newMethods[i] = newMethods[i].copyWith(
        paydate: nowStr,
        customerId: () {
          if (newMethods[i].customerTreatmentCardId != 0) return null;
          return order.customerId;
        }(),
        customerName: () {
          if (newMethods[i].customerTreatmentCardId != 0) return null;
          return order.customerName;
        }(),
      );
    }

    order.addPaymentMethods(newMethods);

    // -- Xử lý thanh toán 1 phần, cập nhật trạng thái ---------------------

    final payTotal = order.getPaymentMethods().sumDouble((e) => e.pay);
    final isPartialPaid = payTotal < state.getTotalAmount();

    if (isPartialPaid) {
      order.paymentStatus = PaymentStatusEnum.partial.id;
    } else {
      order = order
        ..paymentStatus = PaymentStatusEnum.confirmed.id
        ..paidDate = nowStr;

      final COMPLETED_STATUS = _orderUseCase.loadCompletedOrderStatus()?.statusId;

      if (COMPLETED_STATUS != null) {
        order.status = COMPLETED_STATUS;
        for (int i = 0; i < order.orderItems.length; i++) {
          final item = order.orderItems[i];
          if (item.status == OrderStatusEnum.cancelled.id) continue;
          if (item.status == OrderStatusEnum.merged.id) continue;

          if (item.productType == ProductTypeEnum.serviceHourly.id) {
            final timeServiceInfo = item.timeServiceInfo;
            if (timeServiceInfo?.sessions.isNotEmpty ?? false) {
              final lastSession = timeServiceInfo!.sessions.last;
              final result = item.breakdownAndCalculateTotalPrice();

              if (lastSession.end == null) {
                timeServiceInfo.sessions.last = TimeSession(
                  start: lastSession.start,
                  end: DateTime.now(),
                );
              }

              final updated = timeServiceInfo.copyWith(breakdown: result.breakdown, status: 0);
              item.timeOfUse = jsonEncode(updated.toJson());
            }
          }

          order.orderItems[i] = item
            ..status = COMPLETED_STATUS
            ..orderProductVersionCode += 1
            ..productTaxPrice = (isTaxByOrder || notTaxable) ? 0 : item.productTaxPrice;
        }
      }
    }

    // -- Các thông tin khác ------------------------------------------------

    order = order
      ..versionCode += 1
      ..cashierId = UserManager().user.accountId?.toString()
      ..cashierName = UserManager().user.fullName
      ..lastUpdateLocal = nowStr
      ..lastUpdate = nowStr
      ..syncFlag = SyncFlag.prepareSync
      ..paidDeduct = [...state.oldMethods, ...newMethods]
          .nonNulls
          .where((e) => e.noRevenueFlg == 1)
          .toList()
          .sumDouble((e) => e.pay);

    if (order.type == OrderOrigin.booking.id) {
      order.type = OrderOrigin.atShop.id;
    }

    order.setBillInfoValue(
      refundMoney: Wrapped(changeAmount.value),
      totalPaid: Wrapped(wholeAmount + changeAmount.value),
    );

    // Đơn nợ
    if (event.isDebit) {
      order.paidType = 2;
    }

    // Chi trả chiết khấu nhân viên - đối tác
    if (state.displayPartnerPayment == 1 && state.commisionExpenseState.amount > 0) {
      final commisionExpenseState = state.commisionExpenseState;
      final isEmployee = commisionExpenseState.commissionType == CommissionType.employee;
      final accountId = isEmployee
          ? commisionExpenseState.currentStaff?.customerSupplierId
          : commisionExpenseState.currentPartner?.staffCollaboratorPartnerId;
      final accountName =
          isEmployee ? commisionExpenseState.currentStaff?.fullName : commisionExpenseState.currentPartner?.name;
      order.customCommission = jsonEncode([
        {
          "account_id": accountId,
          "account_name": accountName,
          "commission_amount": commisionExpenseState.amount,
          "note": commissionReasonTec.text,
        }
      ]);
    }

    final isPrintKitchen = PrintingHelper.shouldPrintKitchenWhenPayment(order.tableId) && state.printKitchen;

    final needPublishEInvoice =
        state.eInvoiceState.isSelected && state.eInvoiceState.publishType == 1 && order.vatFlg == 1;

    if (CommonFunc.isCashier()) {
      final result = await SaveOrderHelper.saveOrder(
        order,
        source: OrderSource.ui,
        priority: OrderProcessingPriority.ui,
        needPublishEInvoice: needPublishEInvoice,
        syncDelay: isPrintKitchen ? 15 : 0,
        onlineFlg: _onlineFlg,
      );

      if (result.isSuccess) {
        if (PrintingHelper.shouldPrintPaymentCashier() && !state.isSendOrderToCNV) {
          Printing.i.print(
            result.order!,
            typePrint: order.paidType == 2 ? TypePrint.debtBill : TypePrint.payment,
            qrData: event.qrData,
          );
        }

        if (isPrintKitchen) {
          Printing.i
              .print(
            result.order!,
            typePrint: !state.printStamp ? TypePrint.onlyKitchenNotStamp : TypePrint.kitchen,
          )
              .then((printResult) {
            if (printResult.itemPrinted.isNotEmpty) {
              final order = _orderUseCase.loadOrder(result.order!.localId)!;
              order.syncFlag = SyncFlag.prepareSync;
              order.versionCode += 1;
              order.updateTime(all: false);
              for (final item in order.orderItems) {
                if (printResult.itemPrinted.contains(item.orderProductLocalId)) {
                  item.timesPrinted = (item.timesPrinted ?? 0) + 1;
                  item.orderProductVersionCode += 1;
                  item.lastUpdateLocal = SalesUtils.getNowString();
                }
              }

              SaveOrderHelper.saveOrder(
                order,
                source: OrderSource.ui,
                priority: OrderProcessingPriority.ui,
              ).ignore();
            }
          });
        }

        if (state.isSendOrderToCNV) {
          ViewUtils.showToastSuccess('Đơn hàng đã được gửi tới khách hàng ${order.customerName}'.untranslated);
        }
        commitStock();
        successCallback?.call();
      } else {
        ViewUtils.showToastError(result.errorMessage ?? S.current.errorHasOccurred);
      }
    } else {
      final printOptionDevice = PrintingHelper.printOptionDevice();
      if (!_onlineFlg) {
        ViewUtils.showLoading();
        final result = await _messageHandler.sendMessageToCashier(
          DataFromPDA.makePayment(order, printOption: printOptionDevice, qrData: event.qrData),
        );
        await result.processResult(
          onSuccess: (response) async {
            await _handlePdaSendSuccess(response.order!);
            commitStock();
            ViewUtils.showToastSuccess(S.current.paymentSuccess);
          },
          onTimeout: (response) {
            ViewUtils.showToastError(
              response.errorMessage ?? 'Không nhận được phản hồi từ máy thu ngân'.untranslated,
            );
          },
          onFailure: (response, _) {
            ViewUtils.showToastError(
              response.errorMessage ?? S.current.errorHasOccurred,
            );
          },
        );
        ViewUtils.closeLoading();
      } else {
        final result = await SaveOrderHelper.saveOrder(
          order,
          source: OrderSource.ui,
          priority: OrderProcessingPriority.ui,
          needPublishEInvoice: needPublishEInvoice,
          syncDelay: isPrintKitchen ? 15 : 0,
          onlineFlg: _onlineFlg,
        );
        if (result.isSuccess) {
          final printOption = _settingUseCase.getShopOptionsLocal().printOptionDto;
          final havePrintPayment = printOption.isPrintBillWhenPayment == 1;
          final havePrintKitchen = printOption.isPrintKitchenWhenPayment;

          final printPaymentAtPda = printOptionDevice['print_bill_payment'] == 1;
          final printKitchenAtPda = (order.tableId != null && printOptionDevice['print_kitchen_payment_table'] == 1) ||
              (order.tableId == null && printOptionDevice['print_kitchen_payment_counter'] == 1);

          PrintResult? printPaymentResult;
          PrintResult? printKitchenResult;
          bool needUpdateOrder = false;
          final now = DateTime.now();
          final nowString = now.toStringWithFormat(DateTimeFormatConstants.uiDateYmdTimehms);
          if (printPaymentAtPda) {
            ViewUtils.showLoading();
            printPaymentResult = await Printing.i.print(result.order!, qrData: event.qrData);
            if (printPaymentResult.isSuccess) {
              result.order!.numberOfBillsPrinted++;
              needUpdateOrder = true;
            }
          }

          if (printKitchenAtPda) {
            ViewUtils.showLoading();
            printKitchenResult = await Printing.i.print(
              result.order!,
              typePrint: TypePrint.kitchen,
            );
            if (printKitchenResult.itemPrinted.isNotEmpty) {
              for (final item in result.order!.orderItems) {
                if (printKitchenResult.itemPrinted.contains(item.orderProductLocalId)) {
                  item.timesPrinted = (item.timesPrinted ?? 0) + 1;
                  item.lastUpdateLocal = nowString;
                  item.orderProductVersionCode++;
                  needUpdateOrder = true;
                }
              }
            }
          }

          if (needUpdateOrder) {
            result.order!.updateTime(time: now, all: false);
            result.order!.versionCode++;
          }
          // thu ngân cần in
          final typePrints = [
            if (havePrintPayment && !printPaymentAtPda) TypePrint.payment,
            if (havePrintKitchen && !printKitchenAtPda) TypePrint.kitchen,
          ];

          if (needUpdateOrder || typePrints.isNotEmpty) {
            bool needRetry = false;
            do {
              ViewUtils.showLoading();
              final response = await _messageHandler.sendMessageToCashier(
                DataFromPDA.rePrint(
                  order.localId,
                  typePrint: typePrints,
                  order: result.order,
                  qrData: event.qrData,
                ),
              );
              ViewUtils.closeLoading();
              await response.processResult(
                onSuccess: (response) async {
                  needRetry = false;
                },
                onTimeout: (response) async {
                  await ViewUtils.showErrorDialog(
                    CommonValue.currentContext!,
                    message: response.errorMessage ?? 'Không nhận được phản hồi từ máy thu ngân'.untranslated,
                    confirmText: S.current.retry,
                    onConfirmed: () => needRetry = true,
                    onClosed: () => needRetry = false,
                  );
                },
                onFailure: (response, _) {
                  ViewUtils.showToastError(
                    response.errorMessage ?? S.current.errorHasOccurred,
                  );
                  needRetry = false;
                },
              );
            } while (needRetry);
          }

          ViewUtils.closeLoading();
        }
      }
      successCallback?.call();
    }
  }

  void _onUpdateOrderInfoAfterDistributon(OnUpdateOrderInfoAfterDistributon event, Emitter<OrderPaymentState> emit) {
    if (event.order.getExtraDiscounts().isEmpty) discountReasonTec.clear();
    if (event.order.getExtraSurcharges().isEmpty) surchargeReasonTec.clear();
    final newOrderState = state.orderState.update((order) => event.order);
    emit(state.copyWith(orderState: newOrderState, rebuildPromotions: true, rebuildAdjustment: true));
  }

  Future<void> _onChangeEInvoiceState(OnChangeEInvoiceState event, Emitter<OrderPaymentState> emit) async {
    emit(state.copyWith(eInvoiceState: event.state));
  }

  Future<void> shouldRequireCustomerSelectionOrDistibutionDisSur(
    BuildContext context, {
    required VoidCallback onPayOrder,
  }) async {
    if (!EInvoiceManager.i.setting.canExport || !state.eInvoiceState.isSelected) {
      onPayOrder();
      return;
    }

    final order = state.orderState.get();
    if (order.eInvoice.isNullOrEmpty) {
      showEInvoiceInfoPage(context);
    } else {
      final order = state.orderState.get();
      final needDistribution = EInvoiceHelper.needDistribution(order: order);
      if (needDistribution) {
        await CommonFunc.showDialogDistribution(context);
        final newOrder = EInvoiceHelper.distributionDiscountAndSurcharge(orderInput: order);
        add(OnUpdateOrderInfoAfterDistributon(order: newOrder));
      } else {
        onPayOrder();
      }
    }
  }

  void showEInvoiceInfoPage(BuildContext context) {
    final order = state.orderState.get().clone();

    final customer = state.customer;
    final args = EInvoiceInfoPageArgs(
      order: order,
      customer: customer,
      onComfirm: (customer, invoiceType, publishType, sendMailToCustomer) {
        add(
          OnChangeEInvoiceState(
            state: EInvoiceState(
              isSelected: true,
              invoiceType: invoiceType,
              publishType: publishType,
              sendMailToCustomer: sendMailToCustomer,
            ),
          ),
        );
        add(OnChangeCustomer(customer));
      },
    );
    Navigator.of(context).pushNamed(EInvoiceInfoPage.routeName, arguments: args);
  }

  void autoApplyDisSur(int? assetId) {
    final autoDischarges = <DischargeDto>[];
    for (final discount in _presetDiscounts) {
      if (discount.optionApply == DischargeApplyType.all ||
          (discount.optionApply == DischargeApplyType.specificAsset && discount.assetId == assetId)) {
        autoDischarges.add(discount);
      }
    }

    for (final surcharge in _presetSurcharges) {
      if (surcharge.optionApply == DischargeApplyType.all ||
          (surcharge.optionApply == DischargeApplyType.specificAsset && surcharge.assetId == assetId)) {
        autoDischarges.add(surcharge);
      }
    }

    if (autoDischarges.isNotEmpty) {
      add(OnChangeOrderDiscount(null, autoDischarges));
    }
  }

  Future<void> _handlePdaSendSuccess(Order order) async {
    order.syncFlag = SyncFlag.synced;
    await SaveOrderHelper.saveOrder(
      order,
      source: OrderSource.server,
      priority: OrderProcessingPriority.server,
    );

    final isPrintPayment = PrintingHelper.shouldPrintPaymentPda();
    final isPrintKitchen = PrintingHelper.shouldPrintKitchenWhenPayment(order.tableId);

    PrintResult? printPaymentResult;
    PrintResult? printKitchenResult;
    if (isPrintPayment) {
      printPaymentResult = await Printing.i.print(order);
    }

    if (isPrintKitchen) {
      printKitchenResult = await Printing.i.print(
        order,
        typePrint: TypePrint.kitchen,
      );
    }
    bool needUpdate = false;
    if (printPaymentResult?.isSuccess == true) {
      order.numberOfBillsPrinted++;
      needUpdate = true;
    }

    if (printKitchenResult != null) {
      if (printKitchenResult.itemPrinted.isNotEmpty) {
        final lastUpdate = SalesUtils.getNowString();
        for (final item in order.orderItems) {
          if (printKitchenResult.itemPrinted.contains(item.orderProductLocalId)) {
            item.timesPrinted = (item.timesPrinted ?? 0) + 1;
            item.orderProductVersionCode += 1;
            item.lastUpdateLocal = lastUpdate;
          }
        }
        needUpdate = true;
      }
    }

    if (needUpdate) {
      order.updateTime(all: false);
      order.versionCode += 1;
      order.syncFlag = SyncFlag.prepareSync;

      bool shouldRetry = false;

      Future<void> onError(MessageResponse response) async {
        final retry = await showDialog<bool>(
          context: CommonValue.currentContext!,
          builder: (ctx) {
            return CDialog(
              title: S.current.errorHasOccurred,
              centerTitle: true,
              showClose: false,
              actions: [
                CButton.destructive(
                  onTap: () => Navigator.of(ctx).pop(false),
                ),
                CButton.custom(
                  label: S.current.retry,
                  onTap: () => Navigator.of(ctx).pop(true),
                ),
              ],
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CText.body('Không nhận được phản hồi từ máy thu ngân'.untranslated),
                ],
              ),
            );
          },
        );
        shouldRetry = retry ?? false;
      }

      do {
        ViewUtils.showLoading();
        final messageResponse = await _messageHandler.sendMessageToCashier(DataFromPDA.updateOrder(order));
        ViewUtils.closeLoading();

        await messageResponse.processResult(
          onSuccess: (response) {
            SaveOrderHelper.saveOrder(
              response.order!,
              source: OrderSource.server,
              priority: OrderProcessingPriority.server,
            );
          },
          onTimeout: (response) => onError(response),
          onFailure: (response, isNotConnected) {
            if (isNotConnected) {
              shouldRetry = false;
              return;
            }
            onError(response);
          },
        );
      } while (shouldRetry);
    }
  }

  Future<void> _handlePaymentDebt({
    required BuildContext context,
    required double amount,
    required Asset newCurrentAsset,
    required List<PaymentMethodDto>? newAddedMethods,
    required Emitter<OrderPaymentState> emit,
  }) async {
    var customer = state.customer;
    customer ??= await SalesUtils.selectCustomer(context);
    if (customer == null) return;

    add(OnChangeCustomer(customer));

    final isDebit = await OrderPaymentUtils.askDebitForCustomer(
      null,
      money: amount,
      customerName: customer.name ?? '',
    );

    if (!isDebit || !context.mounted) return;

    amountTec.text = NumberFormatUtils.format(amount);

    emit(
      state.copyWith(
        rebuildMethods: true,
        addedMethods: newAddedMethods,
        currentAsset: Wrapped(newCurrentAsset),
        customer: Wrapped(customer),
      ),
    );

    add(OnPayOrder(context: context, isDebit: true));
  }

  Future<void> _handleTreatmentCard({
    required BuildContext context,
    required double amount,
    required String scanCardId,
    required Asset newCurrentAsset,
    required List<PaymentMethodDto>? newAddedMethods,
    required Emitter<OrderPaymentState> emit,
  }) async {
    final order = state.orderState.get();
    final response = await PaymentDirect.handleCardPayment(
      amount: amount,
      scanCardId: scanCardId,
      context: context,
      order: order,
    );
    if (response == null) {
      OrderPaymentUtils.handlePaymentCardResult(isSuccess: false);
      return;
    }

    OrderPaymentUtils.handlePaymentCardResult(message: response.messageWarning);

    final printCardInfo = state.printOption.printPrepaidCardInfo == 1;
    final newOrderState = state.orderState.update(
      (order) => order
        ..customerId = response.data?.customerId
        ..customerName = response.data?.customerName
        ..customerPhoneNumber = response.data?.customerPhone
        ..customerAddress = response.data?.customerAddress
        ..customeEmail = response.data?.customerEmail
        ..customerScanCardBalance = printCardInfo ? response.data?.cardAmountBalance : null
        ..customerScanCardBalanceBeforePay = printCardInfo ? (response.data?.cardAmountBalance ?? 0) + amount : null
        ..customerScanCardPayAmount = printCardInfo ? amount : null,
    );

    final asset = newCurrentAsset.copyWith(
      prepaidCardChargedId: response.data?.customerTreatmentCardId.toString(),
      prepaidCardDebtAmount: response.prepaidCardDebtAmount,
    );

    if (!context.mounted) return;

    amountTec.text = NumberFormatUtils.format(amount);

    emit(
      state.copyWith(
        rebuildMethods: true,
        addedMethods: newAddedMethods,
        currentAsset: Wrapped(asset),
        orderState: newOrderState,
      ),
    );
    add(OnPayOrder(context: context));
  }

  Future<void> _handlePaymentMpos({
    required BuildContext context,
    required double amount,
    required Asset newCurrentAsset,
    required List<PaymentMethodDto>? newAddedMethods,
    required Emitter<OrderPaymentState> emit,
  }) async {
    final order = state.orderState.get();
    final result = await PaymentDirect.createPaymentMpos(asset: newCurrentAsset, amount: amount, order: order);
    if (result.isSuccess) {
      if (!context.mounted) return;
      amountTec.text = NumberFormatUtils.format(amount);
      newCurrentAsset = newCurrentAsset.copyWith(ewalletTransactionLocalId: result.appTransId);
      emit(
        state.copyWith(
          rebuildMethods: true,
          addedMethods: newAddedMethods,
          currentAsset: Wrapped(newCurrentAsset),
        ),
      );
      add(OnPayOrder(context: context));
    } else {
      autoApplyDisSur(state.currentAsset?.assetId);
      ViewUtils.showToastError(result.msg ?? '');
    }
  }

  Future<void> _showPaymentProcess({
    required BuildContext context,
    required double amount,
    required Asset newCurrentAsset,
    required List<PaymentMethodDto>? newAddedMethods,
    required Emitter<OrderPaymentState> emit,
  }) async {
    final order = state.orderState.get();
    await PaymentCorePage.show(
      context: context,
      args: PaymentCorePageArgs(
        order: order,
        asset: newCurrentAsset,
        amount: amount,
        description: '${S.current.payOrder} ${order.codeLocal}',
        onSuccess: (transactionId, qrData) {
          newCurrentAsset = newCurrentAsset.copyWith(ewalletTransactionLocalId: transactionId);
          amountTec.text = NumberFormatUtils.format(amount);
          emit(
            state.copyWith(
              rebuildMethods: true,
              addedMethods: newAddedMethods,
              currentAsset: Wrapped(newCurrentAsset),
            ),
          );
          add(OnPayOrder(context: context, qrData: qrData));
        },
        onUsePrepaidCards: (List<PrepaidCard> prepaidCards) {
          double sum = 0;

          for (final card in prepaidCards) {
            if (card.scanCardId != prepaidCards.last.scanCardId) {
              final remaining = amount - sum;
              final pay = remaining >= card.cardAmountBalance ? card.cardAmountBalance : remaining;

              final asset = newCurrentAsset.copyWith(
                pay: pay,
                cardId: card.id,
                customerId: card.customerId,
                customerName: card.customerName,
              );

              final method = _getCurrentPaymentMethod(newAsset: asset);

              if (method != null) {
                newAddedMethods ??= [];
                newAddedMethods!.add(method);
              }
              sum += pay;
            }
          }

          final lastCard = prepaidCards.last;
          final remaining = amount - sum;
          final pay = remaining >= lastCard.cardAmountBalance ? lastCard.cardAmountBalance : remaining;

          final asset = newCurrentAsset.copyWith(
            pay: pay,
            cardId: lastCard.id,
            customerId: lastCard.customerId,
            customerName: lastCard.customerName,
          );

          amountTec.text = NumberFormatUtils.format(pay);

          emit(
            state.copyWith(
              rebuildMethods: true,
              addedMethods: newAddedMethods,
              currentAsset: Wrapped(asset),
            ),
          );
        },
        onCancel: () {
          autoApplyDisSur(state.currentAsset?.assetId);
        },
      ),
    );
  }

  CommisionExpenseState _getCommisionExpenseInfo(ShopOptions shopOptions) {
    final displayPartnerPayment = shopOptions.displayPartnerPayment;
    if (displayPartnerPayment != 1) return const CommisionExpenseState();
    final staffs = _staffUseCase.loadAllStaff();
    final user = UserManager().user;
    final currentStaff = staffs.firstWhereOrNull((element) => element.customerSupplierId == user.customerSupplierId) ??
        Staff(
          staffId: user.accountId,
          fullName: user.fullName,
          customerSupplierId: user.customerSupplierId,
        );
    if (!staffs.contains(currentStaff)) {
      staffs.add(currentStaff);
    }

    final partners = _staffCollaboratorPartnerUseCase.getAllStaffCollaboratorPartners();
    final configs = shopOptions.commissionExpenseConfig;

    return CommisionExpenseState(
      currentStaff: currentStaff,
      staffs: staffs,
      partners: partners,
      currentPartner: partners.firstOrNull,
      suggestedAmounts: configs.$1,
      customAmount: configs.$2,
    );
  }

  Future<void> _onChangeCommissionType(OnChangeCommissionType event, Emitter<OrderPaymentState> emit) async {
    final currentState = state.commisionExpenseState.copyWith(commissionType: event.type);
    emit(state.copyWith(commisionExpenseState: currentState));
  }

  Future<void> _onChangeCommissionAmount(OnChangeCommissionAmount event, Emitter<OrderPaymentState> emit) async {
    final currentState = state.commisionExpenseState.copyWith(amount: event.amount);
    emit(state.copyWith(commisionExpenseState: currentState));
  }

  Future<void> _onChangeStaffOrPartner(OnChangeStaffOrPartner event, Emitter<OrderPaymentState> emit) async {
    final currentState = state.commisionExpenseState.copyWith(
      currentStaff: event.staff,
      currentPartner: event.partner,
    );
    emit(state.copyWith(commisionExpenseState: currentState));
  }

  Future<void> _onChangePrintFlagWithType(OnChangePrintFlagWithType event, Emitter<OrderPaymentState> emit) async {
    String msg = '';
    switch (event.type) {
      case ChangePrintType.printPayment:
        msg = event.value ? 'Đã bật in thanh toán'.untranslated : 'Đã tắt in thanh toán'.untranslated;
        final printOption = state.printOption.copyWith(isPrintBillWhenPayment: event.value ? 1 : 0);
        _settingUseCase.saveShopOptions(
          updater: (shopOptions) => shopOptions.copyWith(printOption: printOption.parseToStringJson()),
        );
        emit(state.copyWith(printOption: printOption, rebuildAppBar: true));
      case ChangePrintType.printKitchen:
        msg = event.value ? 'Đã bật in bếp'.untranslated : 'Đã tắt in bếp'.untranslated;
        emit(state.copyWith(printKitchen: event.value, rebuildAppBar: true));
      case ChangePrintType.printStamp:
        msg = event.value ? 'Đã bật in tem'.untranslated : 'Đã tắt in tem'.untranslated;
        emit(state.copyWith(printStamp: event.value, rebuildAppBar: true));
    }

    ViewUtils.showToastSuccess(msg);
  }

  void _onRemoveAssetByType(OnRemoveAssetByType event, Emitter<OrderPaymentState> emit) {
    if (state.currentAsset?.type == event.assetType) {
      add(OnRemoveCurrentAsset());
      return;
    }

    final newAddedMethods = List<PaymentMethodDto>.from(state.addedMethods)
      ..removeWhere(
        (e) => e.assetType == event.assetType,
      );

    var customerLoyalty = state.customerLoyalty?.copyWith();

    if (event.assetType == AssetType.point.id) {
      customerLoyalty = customerLoyalty?.copyWith(
        applicablePoint: 0,
        applicablePrice: 0,
      );
    }

    emit(
      state.copyWith(
        rebuildMethods: true,
        addedMethods: newAddedMethods,
        voucherDetail: Wrapped(
          event.assetType == AssetType.voucher.id ? null : state.voucherDetail,
        ),
        customerLoyalty: Wrapped(customerLoyalty),
      ),
    );
  }

  Future<void> _onGetEarnsPoints(OnGetEarnsPoints event, Emitter<OrderPaymentState> emit) async {
    final customer = state.customer;
    if (customer == null || customer.customerId.isNullOrZero) return;

    try {
      final customerId = customer.customerId ?? 0;
      final hasPrepaidCard = customer.scanCardId.isNotNullOrEmpty || customer.customerUserName.isNotNullOrEmpty;

      var prepaidCardsFuture = Future<CustomerPrepaidCards?>.value();
      if (hasPrepaidCard) {
        prepaidCardsFuture = _customerUseCase.getAllCustomerPrepaidCards(
          customerUserName: customer.customerUserName,
          scanCardId: customer.scanCardId,
        );
      }

      final [earnPointsResponse, prepaidCardsResponse] = await Future.wait([
        _customerUseCase.getCustomerPoint(customerId),
        prepaidCardsFuture,
      ]);

      var earnPoints = earnPointsResponse! as EarnPointsDto;
      final cardInfo = prepaidCardsResponse as CustomerPrepaidCards?;

      if (cardInfo != null && cardInfo.prepaidCards.isNotEmpty) {
        earnPoints = earnPoints.copyWith(
          cardAmountBalance: prepaidCardsResponse!.totalBalance,
          scanCardId: customer.scanCardId,
          customerUserName: customer.customerUserName,
        );
      }

      if (earnPoints.memberCardDto != null) {
        add(OnApplyMemberCardDiscount(earnPoints.memberCardDto!));
      }

      final customerLoyalty = SalesUtils.calculateAvailablePoints(earnPoints, state.remainAmount);

      emit(state.copyWith(customerLoyalty: Wrapped(customerLoyalty)));
    } catch (e, s) {
      LogUtils.error(e, s, show: false);
    }
  }

  Future<void> _onApplyMemberCardDiscount(OnApplyMemberCardDiscount event, Emitter<OrderPaymentState> emit) async {
    final memberCard = event.memberCard;
    final isPercent = memberCard.discountType == 0;
    final value = ParseUtils.getDouble(memberCard.discountValue) ?? 0;
    final discount = DischargeDto.program(
      type: PromotionTypeEnum.rankTreatmentCard.id,
      isPercent: isPercent,
      percent: isPercent ? value : 0,
      price: isPercent ? 0 : value,
      reason: 'Chiết khấu hạng thẻ ${memberCard.cardName?.toLowerCase()}', // TODO(khang): translate
      promotionId: memberCard.id,
      applyOnOriginalPriceFlg: 1,
    );

    final newOrderState = state.orderState.update(
      (order) {
        order.addExtraDiscounts([discount]);
        return OrderUtils.recalcDiscounts(order);
      },
    );

    discountReasonTec.text = newOrderState.get().discountReason ?? '';

    final newState = state.copyWith(
      rebuildAdjustment: true,
      rebuildPromotions: true,
      orderState: newOrderState,
    );

    emit(newState);

    _updateRemainAmount(newState);
  }

  @override
  Future<void> close() {
    discountReasonTec.dispose();
    surchargeReasonTec.dispose();
    amountTec.dispose();
    methodScrollController.dispose();
    commissionReasonTec.dispose();
    return super.close();
  }
}
