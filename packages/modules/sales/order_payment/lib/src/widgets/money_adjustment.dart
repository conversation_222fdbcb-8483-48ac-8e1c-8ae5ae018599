import 'package:common/common.dart';
import 'package:data/data.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:resources/resources.dart';
import 'package:sales_core/sales_core.dart';

import '../bloc/order_payment_bloc.dart';

class PaymentMoneyAdjustment extends StatelessWidget {
  const PaymentMoneyAdjustment({super.key});

  @override
  Widget build(BuildContext context) {
    final bloc = context.read<OrderPaymentBloc>();

    return BlocBuilder<OrderPaymentBloc, OrderPaymentState>(
      buildWhen: (prev, curr) => curr.rebuildAdjustment,
      builder: (ctx, state) {
        final order = state.orderState.get();
        final manualDiscount = order.getUnifiedManualDiscount();
        final manualSurcharge = order.getUnifiedManualSurcharge();
        final mapSumTax = state.getMapSumTax();

        return SpacingColumn(
          spacing: Dimens.d16,
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _ItemField.discharge(
              title: S.current.discount,
              value: manualDiscount?.value ?? 0,
              isPercent: manualDiscount?.isPercent ?? true,
              amount: -(manualDiscount?.price ?? 0),
              reasonTec: bloc.discountReasonTec,
              suggestionType: SuggestionEnum.discount,
              onToggle: (isPercent) => bloc.add(OnToggleOrderDiscountPercent(isPercent)),
              onTapValue: () {
                final option = SCMoreAdjustmentOption(
                  getOrder: () => bloc.state.orderState.get(),
                  onSubmitted: (config, items) => bloc.add(OnChangeOrderDiscount(config, items)),
                );
                option.onTapDiscount(context);
              },
              onDelete: () => bloc.add(OnChangeOrderDiscount.delete()),
            ),
            if (manualSurcharge != null)
              _ItemField.discharge(
                title: S.current.surcharge,
                value: manualSurcharge.value,
                isPercent: manualSurcharge.isPercent,
                amount: manualSurcharge.price,
                reasonTec: bloc.surchargeReasonTec,
                suggestionType: SuggestionEnum.surcharge,
                onToggle: (isPercent) => bloc.add(OnToggleOrderSurchargePercent(isPercent)),
                onTapValue: () {
                  final option = SCMoreAdjustmentOption(
                    getOrder: () => bloc.state.orderState.get(),
                    onSubmitted: (config, items) => bloc.add(OnChangeOrderSurcharge(config, items)),
                  );
                  option.onTapSurcharge(context);
                },
                onDelete: () => bloc.add(OnChangeOrderSurcharge.delete()),
              ),
            if (order.taxPercent != 0)
              _ItemField.tax(
                title: S.current.tax,
                value: order.taxPercent,
                amount: order.taxPrice,
              ),
            if (mapSumTax.isNotEmpty)
              Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  for (final key in mapSumTax.keys)
                    CText.body(
                      'Tổng thuế sản phẩm ${NumberFormatUtils.formatPercent(key, 100)}    ${NumberFormatUtils.format(mapSumTax[key])}'
                          .untranslated,
                    ),
                ],
              ),
            if (order.showPartnerInfo)
              _ItemField.partnerDiscount(
                title: 'Chiết khấu'.untranslated,
                value: order.partnerDiscountPercent,
                amount: -order.partnerDiscount,
              ),
          ],
        );
      },
    );
  }
}

class _ItemField extends StatelessWidget {
  final bool _isDischarge;
  final String title;
  final double value;
  final bool isPercent;
  final double amount;
  final String? reason;
  final TextEditingController? reasonTec;
  final SuggestionEnum? suggestionType;
  final void Function(bool isPercent)? onToggle;
  final void Function()? onTapValue;
  final void Function()? onDelete;

  /// Discount / Surcharge
  const _ItemField.discharge({
    required this.title,
    required this.value,
    required this.isPercent,
    required this.amount,
    required this.reasonTec,
    required this.onToggle,
    required this.onTapValue,
    this.onDelete,
    this.suggestionType,
  })  : _isDischarge = true,
        reason = null;

  /// Tax
  const _ItemField.tax({
    required this.title,
    required this.value,
    required this.amount,
  })  : _isDischarge = false,
        isPercent = true,
        reason = null,
        reasonTec = null,
        suggestionType = null,
        onToggle = null,
        onTapValue = null,
        onDelete = null;

  /// Partner discount
  const _ItemField.partnerDiscount({
    required this.title,
    required this.value,
    required this.amount,
  })  : _isDischarge = false,
        isPercent = true,
        reason = null,
        reasonTec = null,
        suggestionType = null,
        onToggle = null,
        onTapValue = null,
        onDelete = null;

  bool get _isTablet => AppDimen.current.isTablet;

  bool get _isEmpty => value == 0 && amount == 0;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          children: [
            Expanded(
              child: CText.body(
                title + (_isDischarge ? '' : ' (${NumberFormatUtils.formatPercent(value, 100)})'),
              ),
            ),
            if (_isDischarge)
              PercentSwitch(
                isPercent: isPercent,
                value: value,
                valueMinWidth: ViewUtils.getScreenWidth(context) / (_isTablet ? 10 : 5),
                onChange: (_) {
                  onToggle?.call(!isPercent);
                  if (value == 0) {
                    onTapValue?.call();
                  }
                },
                onTapValue: onTapValue,
              ),
            CSizedBox.w8(),
            SizedBox(
              height: Dimens.d22,
              width: Dimens.d36,
              child: onDelete != null && !_isEmpty ? SCDeleteButton.orange(onTap: () => onDelete?.call()) : null,
            ),
            Container(
              alignment: Alignment.centerRight,
              constraints: BoxConstraints(
                minWidth: ViewUtils.getScreenWidth(context) / (_isTablet ? 9 : 6),
              ),
              child: _isEmpty
                  ? const SizedBox.shrink()
                  : CText.body(
                      NumberFormatUtils.format(amount, showPositiveSign: true),
                      fontWeight: _isTablet ? FontWeight.bold : FontWeight.normal,
                    ),
            ),
          ],
        ),
        if (_isTablet && _isDischarge && value != 0)
          SuggestableTextField(
            controller: reasonTec,
            // TODO(nam): translate
            title: 'Lý do',
            hintText: 'Nhập lý do ${title.toLowerCase()}',
            onTapSuggest: suggestionType == null
                ? null
                : () async {
                    final text = await SalesUtils.getSuggestionByUser(context, suggestionType!);
                    if (text != null) {
                      reasonTec?.text = text;
                    }
                  },
          )
        else if (reason != null)
          CRichText.titleValue(
            title: S.current.reason,
            value: reason ?? '',
            secondColor: amount > 0 ? CColors.seljukBlue : CColors.destructive,
          ),
      ],
    );
  }
}
